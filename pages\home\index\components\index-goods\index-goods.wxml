<!--pages/home/<USER>/index-goods/index-goods.wxml-->
<wxs module="utils">
  var mainPic = function (pic) {
    return pic.split(",")[0];
  }
  var getPrice = function (goods) {
    if (goods.specType === 1) {
      return goods.goodsItemList[0].price;
    } else if (goods.specType === 2) {
      var price = 0;
      for (var i = 0; i < goods.goodsItemList.length; i++) {
        if (price == 0 || price > goods.goodsItemList[i].price) {
          price = goods.goodsItemList[i].price;
        }
      }
      return price;
    }
    return 0;
  }
  module.exports = { mainPic: mainPic, getPrice: getPrice };
</wxs>

<view class="flex-row container">

  <view class="goods-item-layout" wx:for="{{goodsList}}" wx:key="id" data-goods="{{item}}" bind:tap="toGoodsDetail">
    <view class="flex-column goods-item-container">
      <image class="goods-item-img" mode="aspectFill" src="{{ utils.mainPic(item.pic) }}" />
      <view style="padding: 10rpx 20rpx 6rpx;">
        <view class="lines-1 goods-item-name">{{item.name}}</view>
        <view class="flex-row-center" style="margin-top: 16rpx;justify-content: space-between;">
          <view>
            <text class="goods-item-price1">￥</text>
            <text class="goods-item-price2">{{ utils.getPrice(item) }}</text>
          </view>
          <image class="goods-item-add" src="/static/images/addicon.png" mode="aspectFit" data-goods="{{item}}" catch:tap="toAddGoods" />
        </view>
      </view>
    </view>
  </view>

</view>