/**app.wxss**/
@import 'style/mallicon/iconfont.wxss';

@import 'style/base.wxss';
@import 'style/theme.wxss';

@global {
  font-size: 14rpx; /* rpx是微信小程序推荐的单位 */
}

page {
  font-size: 26rpx;
  --themeColor: #227ae6;
  --themeColorActive: #3486eb;
  --pageBgColor: #F6F6F6;
  /* --pageBgColor: #f6f7fb; */
  --tabbarHeight: 112rpx;
  background-color: #ffffff;
  /* background-color: #f6f7fb; */
  --td-brand-color: var(--themeColor);

  /* 隐藏滚动条，优化分享截图效果 */
  overflow-x: hidden;
}

/* 隐藏所有滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.t-tab-bar {
  --td-brand-color: #227ae6;
  --td-tab-bar-color: #999999;
  --td-tab-item-vertical-height: 200rpx
}