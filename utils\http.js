
import { config } from "~/config/config.js"
import { clearAuth, getToken } from "~/utils/auth.js"

function request(url, data = {}, method = "POST") {
  const headers = {
    'Content-Type': 'application/json'
  };
  if (getToken()) {
    headers.Authorization = 'Bearer ' + getToken();
  }
  return new Promise(function (resolve, reject) {
    wx.request({
      url: config.ApiBaseUrl + url,
      data: data,
      method: method,
      header: headers,
      timeout: 60000,
      dataType: "json",
      success: function (res) {
        console.log(url, res);
        if (res.statusCode == 200) {
          resolve(res.data);
        } else if (res.statusCode == 401) {
          clearAuth();
          reject("认证已失效");
        } else {
          if (res.status === 0) {
            wx.showToast({ title: res.data.msg || "请求失败", icon: 'none', duration: 3000 });
          }
          reject(res.data || "请求失败");
        }
      },
      fail: function (err) {
        reject(err)
      }
    })
  });
}

function uploadFile(url, filePath) {
  const headers = {};
  if (getToken()) {
    headers.Authorization = 'Bearer ' + getToken();
  }
  return new Promise(function (resolve, reject) {
    wx.uploadFile({
      url: config.ApiBaseUrl + url,
      filePath: filePath,
      name: "file",
      header: headers,
      timeout: 60000,
      dataType: "json",
      success: function (res) {
        console.log(url, res);
        if (res.statusCode == 200) {
          resolve(res.data);
        } else if (res.statusCode == 401) {
          clearAuth();
          reject("认证已失效");
        } else {
          wx.showToast({ title: res.data.msg || "请求失败", icon: 'none', duration: 3000 });
          reject(res.data.msg || "请求失败");
        }
      },
      fail: function (err) {
        reject(err)
      }
    })
  });
}
module.exports = { request, uploadFile }