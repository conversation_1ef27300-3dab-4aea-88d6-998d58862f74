/* pages/home/<USER>/personInfo/updateInfo/updateInfo.wxss */
page {
  background-color: var(--pageBgColor);
  height: calc(100vh - env(safe-area-inset-bottom));
}

.submit_btn {
  box-sizing: border-box;
  padding: 64rpx 30rpx 88rpx 30rpx;
  width: 90%;
}

.avatar-wrapper:after {
  border: none;
  background-color: transparent;
}

.avatar-wrapper {
  padding: 0;
  background-color: transparent;
}

.avatar {
  display: block;
  width: 112rpx;
  height: 112rpx;
  border: none;
  background-color: none;
  border-radius: 10rpx;
}

.container {
  display: flex;
}