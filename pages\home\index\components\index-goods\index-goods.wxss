/* pages/home/<USER>/index-goods/index-goods.wxss */
@import '/style/base.wxss';

.container {
  flex-wrap: wrap;
  padding-bottom: 30rpx;
}

.goods-item-layout {
  width: 320rpx;
  margin-left: 35rpx;
  margin-top: 30rpx;
}

.goods-item-layout .goods-item-container {
  margin: 0 auto;
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
}

.goods-item-img {
  width: 100%;
  height: 260rpx;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.goods-item-name {
  font-size: 28rpx;
}

.goods-item-price1 {
  color: #df3c39;
  font-size: 24rpx;
}

.goods-item-price2 {
  margin-left: 8rpx;
  color: #df3c39;
  font-size: 30rpx;
}

.goods-item-add {
  width:40rpx;
  height: 40rpx;
  color: var(--themeColor);
}