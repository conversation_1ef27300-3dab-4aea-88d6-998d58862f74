import TabMenu from './data';
import { checkLogin } from '~/api/AuthApi';

Component({
  data: {
    active: 0,
    list: TabMenu,
  },

  methods: {
    onChange(event) {
      if (event.detail.value == 3) {
        checkLogin().then(res => {
          this.switch(event);
        }).catch((e) => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        });
      } else {
        this.switch(event);
      }
    },
    switch(event) {
      this.setData({ active: event.detail.value })
      wx.switchTab({
        url: this.data.list[event.detail.value].url.startsWith('/')
          ? this.data.list[event.detail.value].url
          : `/${this.data.list[event.detail.value].url}`,
      });
    },
    init() {
      const page = getCurrentPages().pop();
      const route = page ? page.route.split('?')[0] : '';
      const active = this.data.list.findIndex((item) => (item.url.startsWith('/') ? item.url.substr(1) : item.url) === `${route}`);
      this.setData({ active });

      try {
        const tempSwitchTab = wx.getStorageSync('tempSwitchTab')
        if (tempSwitchTab) {
          wx.switchTab({ url: tempSwitchTab });
          wx.removeStorage({ key: 'tempSwitchTab' })
        }
      } catch (error) {
      }

    },
  },
});
