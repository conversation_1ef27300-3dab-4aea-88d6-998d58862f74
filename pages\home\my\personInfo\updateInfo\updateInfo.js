// pages/home/<USER>/personInfo/updateInfo/updateInfo.js
import AuthApi from "~/api/AuthApi.js"
import UploadFileApi from "~/api/UploadFileApi.js"
import { setUserInfo } from "~/utils/auth.js"

Page({
  data: {
    defaultAvatarUrl: '/static/images/avatar.png',
    userInfo: {
      avatar: '',
      nickName: '',
      mobile: '',
    },
    updateNickName: '',
    submitActive: false
  },
  onInputValue(e) {
    const { value = '' } = e.detail;
    this.setData({ submitActive: value !== this.data.userInfo.nickName, updateNickName: value });
  },
  doUpdateInfo() {
    wx.showLoading({ title: '正在保存...', mask: true });
    AuthApi.updateInfo({ avatar: this.data.userInfo.avatar, nickName: this.data.updateNickName || this.data.userInfo.nickName }).then(res => {
      setUserInfo(res);
      this.setData({ submitActive: false });
      wx.hideLoading();
      wx.showToast({ title: '保存成功', icon: 'success', duration: 2000 });
    }).catch((e) => {
      wx.hideLoading();
    });
  },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    wx.showLoading({ title: '请稍后...', mask: true });
    UploadFileApi.upload(avatarUrl).then(res => {
      this.setData({ "userInfo.avatar": JSON.parse(res).path, submitActive: true });
      wx.hideLoading();
    }).catch((e) => {
      wx.hideLoading();
    });

  },
  onLoad(options) {
    AuthApi.requestLoginInfo().then(res => {
      this.setData({ userInfo: res });
    }).catch((e) => {
    });
  },
  onReady() {

  },
  onShow() {

  },
  onHide() {

  },
})