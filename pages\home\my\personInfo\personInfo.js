// pages/home/<USER>/personInfo/personInfo.js
import { requestLoginInfo, checkLogin } from "~/api/AuthApi.js"

Page({
  data: {
    defaultAvatarUrl: '/static/images/avatar.png',
    userInfo: {
      avatar: '',
      nickName: '',
      mobile: '',
    },
  },
  toEditInfo() {
    wx.navigateTo({ url: '/pages/home/<USER>/personInfo/updateInfo/updateInfo' });
  },
  init() {
    requestLoginInfo().then(res => {
      this.setData({ userInfo: res });
    }).catch((e) => {
    });
  },
  onLoad(options) {

  },
  onReady() {

  },
  onShow() {
    this.init();
  },
  onHide() {

  },
})