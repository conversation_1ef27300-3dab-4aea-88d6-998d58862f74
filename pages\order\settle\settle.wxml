<!--pages/order/settle/settle.wxml-->
<wxs module="utils">
  var hidePhoneNum = function (array) { if (!array) return; var mphone = array.substring(0, 3) + '****' + array.substring(7); return mphone; }
  var calcNum = function (list) {
    var num = 0;
    for (var i = 0; i < list.length; i++) {
      num += list[i].buyNum;
    }
    return num;
  }
  var calcPrice = function (list) {
    var price = 0;
    for (var i = 0; i < list.length; i++) {
      price += list[i].goodsItem.price * 100 * list[i].buyNum;
    }
    return price;
  }
  var mainPic = function (pic) { return pic.split(",")[0]; }
  module.exports = { hidePhoneNum: hidePhoneNum, calcNum: calcNum, calcPrice: calcPrice, mainPic: mainPic }
</wxs>

<view class="divider-line" />
<view class="flex-row-center" style="padding: 24rpx 30rpx;border-bottom: 2rpx dashed var(--themeColor);background-color: #FFF;" bind:tap="gotoSelectAddress">
  <t-icon name="location" size="18" style="color: var(--themeColor);" />
  <view wx:if="{{address.id}}" class="flex-column" style="flex: 1;padding: 0rpx 30rpx;">
    <text style="font-size: 26rpx;color: #666;">{{address.provinceName}}{{address.cityName}}{{address.countyName}}</text>
    <text style="font-size: 26rpx;color: #333;font-weight: bold;margin-top: 2rpx;">{{address.detailInfo}}</text>
    <view style="font-size: 24rpx;margin-top: 8rpx;color: #999;">{{ address.bcName }}</view>
    <view class="flex-row-center" style="margin-top: 8rpx;">
      <text style="font-size: 30rpx;color: #333;">{{address.userName}}</text>
      <text style="font-size: 28rpx;margin-left: 20rpx;color: #999;">{{utils.hidePhoneNum(address.telNumber)}}</text>
    </view>
  </view>
  <view wx:else class="flex-column" style="flex: 1;padding: 12rpx 30rpx;">
    <text style="font-size: 28rpx;color: #333;font-weight: bold;">请选择联系人/收货地址</text>
  </view>
  <t-icon name="chevron-right" size="20" color="#BBB" />
</view>
<view class="divider-line" />

<view class="flex-column" style="background-color: #FFF;padding: 0rpx 30rpx 10rpx 30rpx;">
  <view wx:for="{{goodsList}}" class="flex-row" style="padding: 26rpx 0 20rpx;border-bottom: 2rpx solid #EFEFEF;" wx:key="id">
    <view class="goods-info__img">
      <!-- <t-image t-class="goods-info__img_v" shape="round" src="{{ utils.mainPic(item.goodsItem.pic) }}" /> -->
      <image class="goods-info__img_v" src="{{ utils.mainPic(item.goodsItem.pic) }}" />
    </view>
    <view class="goods-info__content">
      <view class="goods-info__title">{{ item.name }}</view>
      <!-- <view class="goods-info__desc">{{ item.goods.goodsDesc }}</view> -->
      <view class="goods-info__spec">
        规格：{{ item.specStr }}
      </view>
    </view>

    <view class="goods-info__right_content">
      <view class="goods-info__price">
        <price price="{{ item.goodsItem.price * 100 }}" fill decimalSmaller />
        <!-- <price style="color: #999;font-size: 30rpx;margin-top: 4rpx;" price="{{ item.goodsItem.price * 100 }}" fill decimalSmaller type="del" /> -->
      </view>
      <view class="goods-info__num">x {{ item.buyNum }}</view>
    </view>

  </view>

  <view class="remark-class">
    <t-input bind:change="remarkInput" label="订单备注" layout="vertical" placeholder="最大输入100个字" borderless="{{true}}" maxlength="{{100}}" />
  </view>

</view>

<view class="bottom_submit_layout">
  <view style="flex: 1;padding-left: 40rpx;">
    <text style="color: #333;font-size: 32rpx;">共{{utils.calcNum(goodsList)}}件，</text>
    <text style="color: #333;font-size: 32rpx;">合计：</text>
    <price style="color: #fa4126;font-weight: bold;font-size: 40rpx;" price="{{utils.calcPrice(goodsList)}}" fill="{{false}}" decimalSmaller />
  </view>
  <view catch:tap="doCreateOrder" style="margin: 0rpx 30rpx;" class="account-btn" hover-class="hover-btn">提交订单</view>
</view>