/* components/audit-result-popup/audit-result-popup.wxss */
.audit-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}

/* 头部区域 */
.popup-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.header-icon {
  margin-bottom: 20rpx;
}

.success-icon {
  color: #52c41a;
}

.fail-icon {
  color: #ff4d4f;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 内容区域 */
.popup-content {
  padding: 0 40rpx 40rpx;
}

.success-content,
.fail-content {
  text-align: center;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.content-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 不通过原因区域 */
.reason-section {
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 30rpx 0 20rpx;
  text-align: left;
}

.reason-title {
  font-size: 28rpx;
  color: #cf1322;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.reason-text {
  font-size: 26rpx;
  color: #595959;
  line-height: 1.5;
  word-break: break-all;
}

/* 审核时间 */
.audit-time {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.time-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.time-value {
  font-size: 24rpx;
  color: #666;
}

/* 底部按钮 */
.popup-footer {
  padding: 0 40rpx 40rpx;
}

.confirm-btn {
  width: 100%;
  height: 76rpx;
  background: linear-gradient(135deg, #2492F2 0%, #1976D2 100%);
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 38rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.confirm-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 双按钮布局 */
.popup-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn,
.retry-btn {
  flex: 1;
  height: 76rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 38rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.cancel-btn:active {
  transform: scale(0.98);
  background-color: #eeeeee;
}

.retry-btn {
  background: linear-gradient(135deg, #2492F2 0%, #1976D2 100%);
  color: #fff;
}

.retry-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}
