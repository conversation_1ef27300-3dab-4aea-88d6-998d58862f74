
import { request } from "~/utils/http.js"

function addInvoice(params) {
  return request('/app/order/addInvoice', { ...params });
}
function orderCreate(params) {
  return request('/app/order/create', { ...params });
}
function getWxPay(params) {
  return request('/app/order/getWxPay', { ...params });
}

function orderList(params) {
  return request('/app/order/list', { ...params });
}
function orderDetail(orderCode) {
  return request('/app/order/info', { orderCode });
}
function orderCancel(orderCode) {
  return request('/app/order/cancel', { orderCode });
}
function orderTakeGoods(orderCode) {
  return request('/app/order/orderTakeGoods', { orderCode });
}

function getOrderNums() {
  return request('/app/order/getOrderNums', {});
}

function addOrderComment(params) {
  return request('/app/orderComment/add', { ...params });
}

function getcommentByGoods(params) {
  return request('/app/orderComment/listByGoods', { ...params });
}

function deleteComment(params) {
  return request('/app/orderComment/delete', { ...params });
}

function getCommentByOrderCode(params) {
  return request('/app/orderComment/getByOrderCode', { ...params });
}

function createAfterSales(params) {
  return request('/app/orderAfterSales/createAfterSales', { ...params });
}

function cancelAfterSales(params) {
  return request('/app/orderAfterSales/cancelAfterSales', { ...params });
}

module.exports = {
  orderCreate, getWxPay, orderList, orderCancel, orderDetail, orderTakeGoods, addInvoice, getOrderNums, addOrderComment, getcommentByGoods, deleteComment,getCommentByOrderCode,createAfterSales,cancelAfterSales
}