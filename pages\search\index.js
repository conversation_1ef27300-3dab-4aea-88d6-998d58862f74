// pages/search/index.js
import GoodsApi from "~/api/GoodsApi.js"

Page({
  data: {
    searchKeyword: '',
    inputFocus: true,
    hasSearched: false,
    showRecommend: true,
    searchHistory: [],
    recommendGoodsList: [], // 推荐商品列表
    searchResultList: [], // 搜索结果列表
    currentGoodsList: [], // 当前显示的商品列表
    sortType: 'default', // 排序类型：default, latest, price, sales
    priceOrder: 'desc', // 价格排序：asc升序, desc降序
    isSpuSelectPopupShow: false, // 商品规格选择弹窗
    goodsInfo: {} // 当前选择的商品信息
  },

  onLoad(options) {
    this.loadSearchHistory();
    this.loadRecommendGoods();
  },

  onShow() {
    // 每次显示页面时重新获取焦点和搜索历史
    this.setData({
      inputFocus: true
    });
    this.loadSearchHistory();
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = wx.getStorageSync('searchHistory') || [];
    this.setData({
      searchHistory: history.slice(0, 10) // 最多显示10条历史记录
    });
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return;
    
    let history = wx.getStorageSync('searchHistory') || [];
    // 移除重复项
    history = history.filter(item => item !== keyword);
    // 添加到开头
    history.unshift(keyword);
    // 最多保存20条
    history = history.slice(0, 20);
    
    wx.setStorageSync('searchHistory', history);
    this.setData({
      searchHistory: history.slice(0, 10)
    });
  },

  // 清除搜索历史
  clearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清除所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('searchHistory');
          this.setData({
            searchHistory: []
          });
        }
      }
    });
  },

  // 选择历史搜索
  selectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchKeyword: keyword
    });
    this.performSearch(keyword);
  },

  // 加载推荐商品（使用goodsList接口数据）
  loadRecommendGoods() {
    GoodsApi.goodsList({ hotGoods: 1 }).then(res => {
      this.setData({
        recommendGoodsList: res.records,
        currentGoodsList: res.records,
      });
    }).catch(err => {
      console.error('加载推荐商品失败:', err);
    });
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    // 实时搜索：当输入内容时自动搜索，清空时恢复推荐商品
    if (keyword.trim()) {
      // 防抖处理，避免频繁调用接口
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.performSearch(keyword.trim());
      }, 500);
    } else {
      // 输入框为空时，恢复到推荐商品状态
      this.resetToRecommend();
    }
  },

  // 执行搜索
  onSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    this.performSearch(keyword);
  },

  // 清空搜索框
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    // 清空搜索框后恢复推荐商品
    this.resetToRecommend();
  },

  // 重置到推荐商品状态
  resetToRecommend() {
    // 清空搜索时重新加载推荐商品
    this.loadRecommendGoods();
    this.setData({
      hasSearched: false,
      showRecommend: true,
      searchResultList: [],
      sortType: 'default'
    });
  },

  // 执行搜索逻辑
  performSearch(keyword) {
    this.saveSearchHistory(keyword);

    // 调用搜索API，使用name字段搜索
    GoodsApi.goodsList({
      name: keyword,
      pageSize: 50
    }).then(res => {
      this.setData({
        hasSearched: true,
        showRecommend: false,
        searchResultList: res.records,
        currentGoodsList: res.records,
        sortType: 'default'
      });
    }).catch(err => {
      console.error('搜索失败:', err);
      this.setData({
        hasSearched: true,
        showRecommend: false,
        searchResultList: [],
        currentGoodsList: []
      });
    });
  },

  // 排序
  onSort(e) {
    const type = e.currentTarget.dataset.type;
    let { searchResultList, priceOrder } = this.data;

    // 如果点击价格排序，切换升降序
    if (type === 'price' && this.data.sortType === 'price') {
      priceOrder = priceOrder === 'asc' ? 'desc' : 'asc';
    } else if (type === 'price') {
      priceOrder = 'desc';
    }

    // 执行排序 - 基于搜索结果进行排序
    const sortedList = this.sortGoodsList([...searchResultList], type, priceOrder);

    console.log(`排序类型: ${type}, 价格顺序: ${priceOrder}`);
    if (type === 'price') {
      console.log('排序前价格:', searchResultList.slice(0, 3).map(item => this.getGoodsPrice(item)));
      console.log('排序后价格:', sortedList.slice(0, 3).map(item => this.getGoodsPrice(item)));
    }

    this.setData({
      sortType: type,
      priceOrder: priceOrder,
      currentGoodsList: sortedList
    });
  },

  // 获取商品价格的辅助方法
  getGoodsPrice(goods) {
    if (!goods || !goods.goodsItemList || goods.goodsItemList.length === 0) {
      return 0;
    }

    if (goods.specType === 1) {
      // 单规格商品，直接取第一个商品项的价格
      return parseFloat(goods.goodsItemList[0].price) || 0;
    } else if (goods.specType === 2) {
      // 多规格商品，取最低价格
      let price = 0;
      for (let i = 0; i < goods.goodsItemList.length; i++) {
        const itemPrice = parseFloat(goods.goodsItemList[i].price) || 0;
        if (price === 0 || price > itemPrice) {
          price = itemPrice;
        }
      }
      return price;
    }
    return 0;
  },

  // 商品列表排序逻辑
  sortGoodsList(list, type, priceOrder = 'desc') {
    if (!list || list.length === 0) return [];

    switch (type) {
      case 'latest':
        // 按创建时间排序
        return list.sort((a, b) => {
          const timeA = new Date(a.createTime || a.updateTime || 0);
          const timeB = new Date(b.createTime || b.updateTime || 0);
          return timeB - timeA;
        });
      case 'price':
        // 按价格排序
        return list.sort((a, b) => {
          const priceA = this.getGoodsPrice(a);
          const priceB = this.getGoodsPrice(b);
          return priceOrder === 'asc' ? priceA - priceB : priceB - priceA;
        });
      case 'sales':
        // 按销量排序
        return list.sort((a, b) => {
          const salesA = parseInt(a.soldCount) || 0;
          const salesB = parseInt(b.soldCount) || 0;
          return salesB - salesA;
        });
      default:
        // 综合排序：综合考虑销量、价格、时间等因素
        return list.sort((a, b) => {
          const salesA = parseInt(a.soldCount) || 0;
          const salesB = parseInt(b.soldCount) || 0;
          const priceA = this.getGoodsPrice(a);
          const priceB = this.getGoodsPrice(b);

          // 综合评分：销量权重0.6，价格权重0.4（价格越低分数越高）
          const scoreA = salesA * 0.6 + (priceA > 0 ? (1000 / priceA) * 0.4 : 0);
          const scoreB = salesB * 0.6 + (priceB > 0 ? (1000 / priceB) * 0.4 : 0);

          return scoreB - scoreA;
        });
    }
  },

  // 显示筛选
  showFilter() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 跳转商品详情
  toGoodsDetail(e) {
    const goods = e.currentTarget.dataset.goods;
    wx.navigateTo({
      url: `/pages/goodsDetail/goodsDetail?goodsId=${goods.id}`
    });
  },

  // 添加到购物车
  addToCart(e) {
    const goods = e.currentTarget.dataset.goods;
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    });
    // 这里可以调用添加购物车的API
    console.log('添加到购物车:', goods);
  },

  // index-goods组件的添加商品事件 - 弹出规格选择弹窗
  toAddGoods(e) {
    this.setData({
      isSpuSelectPopupShow: true,
      goodsInfo: e.detail.goods
    });
    const goodsSpecsPopup = this.selectComponent('#goodsSpecsPopup');
    goodsSpecsPopup.init();
  },

  // 立即购买
  doBuyNow(e) {
    this.handlePopupHide(() => {
      const params = e.detail;
      wx.navigateTo({
        url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}`
      });
    });
  },

  // 添加到购物车
  doAddCart(params) {
    this.handlePopupHide();
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    });
  },

  // 关闭弹窗
  handlePopupHide(func) {
    this.setData({
      isSpuSelectPopupShow: false
    }, func);
  },

  // 页面卸载时清理定时器
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
});
