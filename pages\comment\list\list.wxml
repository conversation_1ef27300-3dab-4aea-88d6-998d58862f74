<!--pages/comment/list/list.wxml-->
<wxs module="utils">
  function getFirstChar(userName, userNickname, nickName, userNickName) {
    var name = userName || userNickname || nickName || userNickName || '匿名用户';
    return name.charAt(0);
  }
  function getUserName(userName, userNickname, nickName, userNickName) {
    return userName || userNickname || nickName || userNickName || '匿名用户';
  }
  module.exports = { getFirstChar: getFirstChar, getUserName: getUserName };
</wxs>

<view class="comment-list-container">
  <!-- 评价统计和筛选 -->
  <view class="comment-header-section">
    <!-- 评价总分区域 -->
    <!-- <view class="rating-summary" wx:if="{{total > 0}}">
      <view class="rating-info">
        <view class="average-score">{{averageRating}}</view>
        <view class="rating-stars">
          <van-rate
            value="{{averageRating}}"
            size="16"
            color="#2492F2"
            void-color="#E5E5E5"
            gutter="2"
            readonly="{{true}}"
            allow-half="{{true}}"
          />
        </view>
        <view class="total-count">共{{total}}条评价</view>
      </view>
    </view>
    <view class="lineHeighe"></view>
     -->
    <!-- 筛选标签 -->
    <van-tabs
      active="{{activeTab}}"
      bind:change="onTabChange"
      color="#2492F2"
      title-active-color="#2492F2"
      title-inactive-color="#666"
      background="#fff"
    >
      <van-tab title="全部" name="all"></van-tab>
      <van-tab title="好评{{goodCount > 0 ? '(' + goodCount + ')' : ''}}" name="good"></van-tab>
      <van-tab title="中评{{mediumCount > 0 ? '(' + mediumCount + ')' : ''}}" name="medium"></van-tab>
      <van-tab title="差评{{badCount > 0 ? '(' + badCount + ')' : ''}}" name="bad"></van-tab>
    </van-tabs>
  </view>

  <!-- 评价列表 -->
  <view class="comment-list" wx:if="{{commentList.length > 0}}">
    <view class="comment-item" wx:for="{{commentList}}" wx:key="id">
      <!-- 用户信息 -->
      <view class="user-info">
        <view class="user-avatar" wx:if="{{!item.userAvatar}}">
          <text class="avatar-text">{{utils.getFirstChar(item.userName, item.userNickname, item.nickName, item.userNickName)}}</text>
        </view>
        <image class="user-avatar-img" wx:else src="{{item.userAvatar}}" mode="aspectFill" />
        <view class="user-details">
          <view class="user-name">{{utils.getUserName(item.userName, item.userNickname, item.nickName, item.userNickName)}}</view>
          <view class="comment-time">{{item.createTime}}</view>
        </view>
        <view class="rating-stars">
          <van-rate
            value="{{item.rating}}"
            size="14"
            color="#2492F2"
            void-color="#E5E5E5"
            gutter="2"
            readonly="{{true}}"
            allow-half="{{false}}"
          />
        </view>
      </view>

      <!-- 评价内容 -->
      <view class="comment-content">
        <text>{{item.content}}</text>
      </view>

      <!-- 商品信息 -->
      <view class="goods-info" wx:if="{{item.goodsName}}">
        <text class="goods-name">{{item.goodsName}}</text>
        <text class="goods-spec" wx:if="{{item.specDesc}}">{{item.specDesc}}</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{commentList.length === 0 && !loading}}">
    <view class="empty-icon">💬</view>
    <view class="empty-text">暂无评价</view>
    <view class="empty-desc">快来成为第一个评价的人吧</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!hasMore && commentList.length > 0}}">
    <text>没有更多评价了</text>
  </view>
</view>
