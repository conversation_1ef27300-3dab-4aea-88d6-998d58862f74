.custom-sidebar {
  width: 160rpx;
  height: calc(100vh - var(--tabbarHeight));
  /* background-color: #F6F6F6; */
}

.custom-sidebar::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.c-sidebar {
  max-height: calc(100vh - var(--tabbarHeight));
}

.c-sidebar ::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.c-sidebar-layer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.c-sidebar-item {
  width: 120rpx;
  height: 134rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2rpx solid rgba(255, 255, 255, 0);
  border-radius: 10rpx;
  box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.5);
}

.c-sidebar-item:nth-child(n + 1) {
  margin-top: 15rpx;
  margin-bottom: 15rpx;
  background-color: #FBFBFB;
}

.c-sidebar-item.selected {
  border: 2rpx solid var(--themeColor);
  box-shadow: 0 0 12rpx 0 var(--themeColor);
  background-color: #FFFFFF;
}

.c-sidebar-icon {
  display: flex;
  width: 70rpx;
  height: 70rpx;
  justify-content: center;
  align-items: center;
  /* background-color: var(--themeColor); */
  /* border-radius: 999rpx; */
}

.c-sidebar-icon .c-sidebar-icon-image {
  width: 50rpx;
  height: 50rpx;
}

.c-sidebar-item-txt {
  font-size: 28rpx;
  margin-top: 5rpx;
  color: #333333;
}