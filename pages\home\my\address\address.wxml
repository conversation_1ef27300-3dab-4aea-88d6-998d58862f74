<!--pages/my/address.wxml-->
<wxs module="utils">
  var hidePhoneNum = function (array) { if (!array) return; var mphone = array.substring(0, 3) + '****' + array.substring(7); return mphone; }
  module.exports = { hidePhoneNum: hidePhoneNum }
</wxs>

<view style="display: flex;padding: 24rpx 30rpx;border-bottom: 2rpx dashed var(--themeColor);background-color: #FFF;" bind:tap="gotoAdd">
  <t-icon name="location" size="20" style="color: var(--themeColor);" />
  <text style="font-size: 34rpx;margin-left: 14rpx;">新增联系方式/收货地址</text>
</view>

<view style="background-color: #fff;">
  <t-swipe-cell class="swipe-out" wx:for="{{ addressList }}" wx:key="id">
    <view class="address-item" catch:tap="doSelect" data-item="{{item}}">
      <view class="address-item-left">
        <view class="address-item-head">
          <text wx:if="{{ item.defaultFlag == 1 }}" class="address-item-default">默认</text>
          <text class="address-item-name">{{ item.userName }}</text>
          <text class="address-item-phone">{{ utils.hidePhoneNum(item.telNumber) }}</text>
        </view>
        <view class="address-item-info2">{{ item.bcName }}</view>
        <view class="address-item-info">{{ item.detailInfo }}</view>
      </view>
      <view class="address-item-edit" catch:tap="gotoEdit" data-item="{{item}}">
        <t-icon name="edit-2" size="20" color="var(--themeColor)" />
      </view>
    </view>
    <view slot="right" class="swipe-right-del" bind:tap="onDelete" data-item="{{item}}"> 删除 </view>
  </t-swipe-cell>
</view>