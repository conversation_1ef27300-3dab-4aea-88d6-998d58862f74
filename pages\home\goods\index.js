// pages/goods/index.js
import { goodsTypeList } from "~/api/GoodsTypeApi.js"
import * as GoodsApi from "~/api/GoodsApi.js"
import { shopCarList } from "~/api/ShopCarApi.js"
import { checkLogin } from '~/api/AuthApi';

Page({
  data: {
    typeList: [],
    selectTypeId: '',
    activeTabIndex: 0,
    goodsList: [],
    cartList: [],
    showGoods: [],
    isSpuSelectPopupShow: false,
    goodsInfo: {},
    tempId: ""
  },
  doBuyNow(e) {
    this.handlePopupHide(() => {
      const params = e.detail;
      wx.navigateTo({ url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}` });
    });
  },
  doAddCart(params) {
    this.handlePopupHide();
  },
  handlePopupHide(func) {
    this.setData({ isSpuSelectPopupShow: false, }, func);
  },
  toAddGoods(e) {
    this.setData({ isSpuSelectPopupShow: true, goodsInfo: e.detail.goods });
    const goodsSpecsPopup = this.selectComponent('#goodsSpecsPopup');
    goodsSpecsPopup.init();
  },
  getClassifyData() {
    wx.showLoading({ title: '正在加载...', mask: true });
    const selectedBuildingId = wx.getStorageSync('selectedBuildingId');
    GoodsApi.classifyData(2, selectedBuildingId).then(res => {
      this.setData({ typeList: res.typeList, goodsList: res.goods });
      if (this.data.tempId) {
        const tempIndex = this.data.typeList.findIndex(item => item.id === this.data.tempId);
        this.setData({
          selectTypeId: this.data.tempId,
          activeTabIndex: tempIndex >= 0 ? tempIndex : 0,
          tempId: ""
        });
        this.setData({
          showGoods: this.data.goodsList.find(item => item.id === this.data.selectTypeId).goodsList,
        });
      } else {
        if (this.data.typeList.length > 0) {
          this.setData({
            selectTypeId: this.data.typeList[0].id,
            activeTabIndex: 0
          });
          this.setData({
            showGoods: this.data.goodsList.find(item => item.id === this.data.selectTypeId).goodsList,
          });
        }
      }
      wx.hideLoading();
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
    });
  },
  onTabsChange(e) {
    const { index, name } = e.detail;
    this.setData({
      activeTabIndex: index,
      selectTypeId: name
    });
    this.setData({
      showGoods: this.data.goodsList.find(item => item.id === this.data.selectTypeId).goodsList,
    });
  },
  getCartList() {
    shopCarList({}).then(res => {
      this.setData({ cartList: res });
    }).catch(error => {
      console.log(error);
    });
  },
  onLoad(options) {
    this.getClassifyData();

    // 设置分享菜单
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  onReady() {
  },
  onShow() {
    this.getTabBar().init();
    try {
      const tempId = wx.getStorageSync('tempId')
      if (tempId) {
        this.setData({ tempId: tempId });
        wx.removeStorage({ key: 'tempId' })
        if (this.data.goodsList.length > 0) {
          this.setData({ selectTypeId: this.data.tempId, tempId: "" });
          this.setData({
            showGoods: this.data.goodsList.find(item => item.id === this.data.selectTypeId).goodsList,
          });
        }
      }
    } catch (error) {
    }

  },
  onHide() {

  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '楼宇企业全生命周期生态服务 - 商城',
      path: '/pages/home/<USER>/index',
      imageUrl: 'https://hqt.csylch.com/platform/hqt.jpg'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '楼宇企业全生命周期生态服务 - 商城',
      imageUrl: 'https://hqt.csylch.com/platform/hqt.jpg'
    };
  }
})