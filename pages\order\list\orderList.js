// pages/order/list/orderList.js
import * as OrderApi from '~/api/OrderApi';
import { checkLogin } from '~/api/AuthApi';

Page({
  data: {
    tabIndex: 0,
    orderList: [],
    showCommentPopup: false,
    currentComment: {},
    currentOrderInfo: {},
    auditPopupVisible: false,
    currentAuditResult: {},
    showPaymentActionSheet: false,
    showOfflinePaymentOverlay: false,
    currentPaymentOrder: null,
    paymentActions: [
      {
        name: '微信支付',
        color: '#07c160'
      },
      {
        name: '线下支付',
        color: '#1989fa'
      }
    ]
  },
  onRefresh({ detail: { isRefresh, current, size } }) {
    const params = {};
    if (this.data.tabIndex > 0) {
      params.status = this.data.tabIndex;
    }
    OrderApi.orderList({ ...params, current, size }).then(res => {
      // 调试：打印订单数据，检查orderAfterSales结构
      console.log('订单列表数据:', res.records);
      res.records.forEach((order, index) => {
        if (order.orderAfterSales) {
          console.log(`订单${index} - orderAfterSales:`, order.orderAfterSales);
        }
      });

      if (isRefresh) {
        this.setData({ orderList: res.records });
      } else {
        this.setData({ orderList: this.data.orderList.concat(res.records) });
      }
      const useDrawer = this.selectComponent("#refresherLoader");
      useDrawer.complete({ total: res.total });
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
      const useDrawer = this.selectComponent("#refresherLoader");
      useDrawer.completeError();
    });
  },
  toDetail(e) {
    const { item } = e.currentTarget.dataset;
    wx.navigateTo({ url: `/pages/order/orderDetail/orderDetail?orderCode=${item.orderCode}` });
  },
  // 付款 - 显示支付方式选择
  doPay(e) {
    const { item } = e.currentTarget.dataset;
    this.setData({
      currentPaymentOrder: item,
      showPaymentActionSheet: true
    });
  },

  // 选择支付方式
  onSelectPaymentMethod(event) {
    console.log('选择支付方式事件:', event.detail);
    const selectedItem = event.detail;

    this.setData({
      showPaymentActionSheet: false
    });

    if (selectedItem.name === '微信支付') {
      // 微信支付
      console.log('选择了微信支付');
      this.doWechatPay();
    } else if (selectedItem.name === '线下支付') {
      // 线下支付
      console.log('选择了线下支付');
      this.showOfflinePaymentInfo();
    }
  },

  // 关闭支付方式选择面板
  onClosePaymentActionSheet() {
    this.setData({
      showPaymentActionSheet: false,
      currentPaymentOrder: null
    });
  },

  // 微信支付逻辑（原有逻辑）
  doWechatPay() {
    const orderItem = this.data.currentPaymentOrder;
    if (!orderItem) return;

    console.log('开始微信支付流程');
    wx.showLoading({ title: '正在请求...', mask: true });
    OrderApi.getWxPay({ orderCode: orderItem.orderCode }).then(res => {
      console.log('微信支付API响应:', res);
      wx.hideLoading();
      wx.requestPayment({
        timeStamp: res.timeStamp,
        nonceStr: res.nonceStr,
        package: res.packageValue,
        signType: res.signType,
        paySign: res.paySign,
        success: (res) => {
          console.log('微信支付成功:', res);
          wx.showToast({ title: '支付成功', icon: 'success', duration: 2000 });
          this.initRefresh();
        },
        fail: (res) => {
          console.log('微信支付失败:', res);
        }
      });
    }).catch(error => {
      console.log('微信支付API错误:', error);
      wx.hideLoading();
    });
  },

  // 显示线下支付信息
  showOfflinePaymentInfo() {
    console.log('显示线下支付信息');
    this.setData({
      showOfflinePaymentOverlay: true
    });
  },

  // 关闭线下支付信息
  closeOfflinePaymentInfo() {
    this.setData({
      showOfflinePaymentOverlay: false,
      currentPaymentOrder: null
    });
  },

  // 一键复制支付信息
  copyPaymentInfo() {
    const paymentInfo = `账户名称：长沙耀楼产业互联科技服务有限公司
账户号码：731912140110006
开户银行：招商银行股份有限公司长沙晓园支行
纳税识别号：91430104MAEBFJ0G7C
公司地址：湖南省长沙市岳麓区岳麓街道溁左路中南大学科技园研发总部 6 栋 322 房`;

    wx.setClipboardData({
      data: paymentInfo,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击事件冒泡到遮罩层
  },
  // 取消订单
  doCancelOrder(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认取消订单？',
      success(res) {
        if (res.confirm) {
          const { item } = e.currentTarget.dataset;
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.orderCancel(item.orderCode).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.initRefresh();
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  // 确认收货
  doTakeGoods(e) {
    const _that = this;
    const { item } = e.currentTarget.dataset;

    // 检查是否有退款申请在审核中
    if (item.orderAfterSales && item.orderAfterSales.serviceStatus === 'user_apply') {
      // 有退款申请在审核中，需要特殊提示
      wx.showModal({
        title: '提示',
        content: '您提交了退款申请，是否确认收货？',
        confirmText: '确认收货',
        cancelText: '取消',
        success(res) {
          if (res.confirm) {
            _that.executeReceiveGoods(item);
          }
          // 用户取消则不做任何处理
        }
      });
    } else {
      // 正常确认收货流程
      wx.showModal({
        title: '提示',
        content: '确认完成订单？',
        success(res) {
          if (res.confirm) {
            _that.executeReceiveGoods(item);
          }
        }
      });
    }
  },

  // 执行收货操作
  executeReceiveGoods(item) {
    const _that = this;
    wx.showLoading({ title: '正在请求...', mask: true });
    OrderApi.orderTakeGoods(item.orderCode).then(res => {
      wx.hideLoading();
      wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });

      // 收货成功后跳转到评价页面
      setTimeout(() => {
        _that.navigateToComment(item);
      }, 2000);

    }).catch(error => {
      console.log(error);
      wx.hideLoading();
    });
  },

  // 申请退款
  doApplyRefund(e) {
    const { item } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/order/refund/refund?orderCode=${item.orderCode}`
    });
  },

  // 撤销申请
  async doCancelAfterSales(e) {
    const { item } = e.currentTarget.dataset;
    const afterSalesId = item.orderAfterSales.afterSalesId;

    try {
      await wx.showModal({
        title: '确认撤销',
        content: '确定要撤销退款申请吗？',
        confirmText: '确定',
        cancelText: '取消'
      });

      wx.showLoading({ title: '处理中...', mask: true });

      const dataJson = await OrderApi.cancelAfterSales({ afterSalesId });
      if(dataJson.serviceStatus == "cancel"){
        wx.showToast({
          title: '撤销成功',
          icon: 'success'
        });
      }
      // 刷新订单列表
      this.selectComponent('#refresherLoader').handleRefresh();

    } catch (error) {
      if (error.errMsg && error.errMsg.includes('cancel')) {
        // 用户取消操作
        return;
      }
      console.error('撤销申请失败:', error);
      wx.showToast({
        title: error.message || '撤销失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 查看审核结果
  doViewAuditResult(e) {
    const { item } = e.currentTarget.dataset;
    const afterSales = item.orderAfterSales;

    if (!afterSales) {
      return;
    }

    const { auditResult, auditRemark, auditTime } = afterSales;

    // 通过和未通过都使用统一的自定义弹窗
    this.setData({
      currentAuditResult: {
        auditResult,
        auditRemark,
        auditTime,
        orderCode: item.orderCode
      },
      auditPopupVisible: true
    });
  },

  // 关闭审核结果弹窗
  onCloseAuditPopup() {
    this.setData({
      auditPopupVisible: false
    });

    // 延迟清空数据，避免弹窗关闭动画期间数据变化导致闪烁
    setTimeout(() => {
      this.setData({
        currentAuditResult: {
          auditResult: '',
          auditRemark: '',
          auditTime: '',
          orderCode: ''
        }
      });
    }, 300);
  },

  // 重新发起退款
  onRetryRefund() {
    const { orderCode } = this.data.currentAuditResult;

    // 关闭弹窗
    this.onCloseAuditPopup();

    // 跳转到申请退款页面
    wx.navigateTo({
      url: `/pages/order/refund/refund?orderCode=${orderCode}`
    });
  },

  // 跳转到评价页面
  navigateToComment(orderItem) {
    if (orderItem.orderItemList && orderItem.orderItemList.length > 0) {
      // 取第一个商品信息作为评价对象
      const goodsInfo = orderItem.orderItemList[0];
      const goodsInfoStr = encodeURIComponent(JSON.stringify(goodsInfo));
      wx.navigateTo({
        url: `/pages/comment/add/add?orderCode=${orderItem.orderCode}&goodsInfo=${goodsInfoStr}`
      });
    }
  },

  // 去评价
  doAddComment(e) {
    const { item } = e.currentTarget.dataset;
    this.navigateToComment(item);
  },

  // 查看评价
  async doViewComment(e) {
    const { item } = e.currentTarget.dataset;

    try {
      wx.showLoading({ title: '加载中...', mask: true });

      // 调试：打印请求参数
      console.log('请求参数:', { orderCode: item.orderCode });

      // 调用getCommentByOrderCode接口获取当前订单的评价
      const result = await OrderApi.getCommentByOrderCode({
        orderCode: item.orderCode
      });

      wx.hideLoading();

      // 调试：打印接口返回的完整数据
      console.log('getCommentByOrderCode接口返回:', result);
      console.log('result类型:', typeof result);
      console.log('result是否为数组:', Array.isArray(result));
      console.log('result长度:', result ? result.length : 'undefined');

      // 检查不同的数据结构可能性
      let commentData = null;

      if (result) {
        // 可能的数据结构1: 直接返回数组
        if (Array.isArray(result) && result.length > 0) {
          commentData = result[0];
          console.log('匹配数据结构1: 直接返回数组');
        }
        // 可能的数据结构2: 返回对象，数据在records字段中
        else if (result.records && Array.isArray(result.records) && result.records.length > 0) {
          commentData = result.records[0];
          console.log('匹配数据结构2: 数据在records字段中');
        }
        // 可能的数据结构3: 返回对象，数据在data字段中
        else if (result.data && Array.isArray(result.data) && result.data.length > 0) {
          commentData = result.data[0];
          console.log('匹配数据结构3: 数据在data字段中');
        }
        // 可能的数据结构4: 直接返回单个评价对象
        else if (result.id || result.content || result.rating) {
          commentData = result;
          console.log('匹配数据结构4: 直接返回单个评价对象');
        }
        // 可能的数据结构5: 检查是否有其他可能的字段
        else {
          console.log('未匹配任何已知数据结构，尝试查找其他字段:');
          console.log('result的所有键:', Object.keys(result));

          // 尝试查找可能包含评价数据的字段
          for (let key in result) {
            if (Array.isArray(result[key]) && result[key].length > 0) {
              console.log(`发现数组字段 ${key}:`, result[key]);
              commentData = result[key][0];
              break;
            }
          }
        }
      }

      console.log('解析后的评价数据:', commentData);

      if (commentData) {
        // 显示评价详情弹窗
        this.setData({
          showCommentPopup: true,
          currentComment: commentData,
          currentOrderInfo: item
        });
      } else {
        console.log('未找到评价数据，显示暂无评价提示');
        wx.showToast({
          title: '暂无评价信息',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('获取评价失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '获取评价失败',
        icon: 'none'
      });
    }
  },

  // 关闭评价详情弹窗
  onCloseCommentPopup() {
    this.setData({
      showCommentPopup: false,
      currentComment: {},
      currentOrderInfo: {}
    });
  },
  initRefresh() {
    const useDrawer = this.selectComponent("#refresherLoader");
    if (useDrawer) {
      useDrawer.handleRefresh();
    }
  },
  onTabsChange(e) {
    this.setData({ tabIndex: e.detail.value });
    this.initRefresh();
  },
  onLoad(options) {
    if (options.which) {
      this.setData({ tabIndex: options.which });
    } else {
      this.setData({ tabIndex: 0 });
    }
  },
  onReady() {
  },
  onShow() {
    checkLogin().then(res => {
      this.initRefresh();
    }).catch((e) => {
      wx.showModal({
        title: '提示',
        content: '当前未登录',
        confirmText: '前去登录',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          } else if (res.cancel) {
          }
        }
      });
    });
  }

})