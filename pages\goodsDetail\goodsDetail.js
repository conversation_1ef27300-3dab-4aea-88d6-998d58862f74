// pages/goodsDetail/goodsDetail.js
import * as GoodsApi from "~/api/GoodsApi.js"
import * as ShopCarApi from "~/api/ShopCarApi.js"
import * as OrderApi from "~/api/OrderApi.js"

Page({
  data: {
    goodsId: '',
    swiperList: [],
    goodsInfo: {},
    currentPrice: 0,
    buyType: 0,
    isSpuSelectPopupShow: false,
    imageViewerIndex: 0,
    imageViewerVisible: false,
    commentList: [],
    commentTotal: 0,
    averageRating: 0
  },
  doBuyNow(e) {
    this.handlePopupHide(() => {
      const params = e.detail;
      wx.navigateTo({ url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}` });
    });
  },
  doAddCart(params) {
    // const params = e.detail;
    // wx.navigateTo({ url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}` });
    this.handlePopupHide();
  },
  async init(id) {
    try {
      this.setData({ goodsId: id });
      const result = await GoodsApi.goodsInfo({ id });
      this.setData({ goodsInfo: result, swiperList: result.pic.split(",") });
      if (this.data.goodsInfo.specType === 1) {
        this.setData({ currentPrice: this.data.goodsInfo.goodsItemList[0].price });
      } else if (this.data.goodsInfo.specType === 2) {
        let price = 0;
        for (let i = 0; i < this.data.goodsInfo.goodsItemList.length; i++) {
          if (price == 0 || price > this.data.goodsInfo.goodsItemList[i].price) {
            price = this.data.goodsInfo.goodsItemList[i].price;
          }
        }
        this.setData({ currentPrice: price });
      }

      // 加载评价数据
      this.loadCommentData(id);
    } catch (error) {
      console.error('err:', error);
    }
  },
  showSkuSelectPopup(type) {
    this.setData({ buyType: type || 0, isSpuSelectPopupShow: true });
    const goodsSpecsPopup = this.selectComponent('#goodsSpecsPopup');
    goodsSpecsPopup.init();
  },
  handlePopupHide(func) {
    this.setData({ isSpuSelectPopupShow: false, }, func);
  },
  toHome() {
    wx.navigateBack();
  },
  toCart() {
    wx.setStorageSync('tempSwitchTab', "/pages/home/<USER>/index");
    wx.navigateBack();
  },
  toBuyNow() {
    this.showSkuSelectPopup(1);
  },
  toAddCart() {
    this.showSkuSelectPopup(2);
  },
  // 联系商家 - 使用企业微信客服
  contactMerchant() {
    // 检查API是否存在并且是函数
    if (wx.openCustomerServiceChat && typeof wx.openCustomerServiceChat === 'function') {
      wx.openCustomerServiceChat({
        extInfo: {
          url: 'https://work.weixin.qq.com/kfid/kfca89776bb2fbab2da' // 用户指定的客服链接
        },
        corpId: 'ww36fad4ed63da23c7', // 企业ID
        success: (res) => {
          console.log('打开企业客服成功', res);
        },
        fail: (err) => {
          console.log('打开企业客服失败', err);
          // API调用失败时的备用方案
          this.showCustomerServiceOptions();
        }
      });
    } else {
      console.log('当前环境不支持 openCustomerServiceChat API');
      // API不存在时的备用方案
      this.showCustomerServiceOptions();
    }
  },
  // 显示客服联系方式选项
  showCustomerServiceOptions() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '查看联系方式'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 拨打电话
            wx.makePhoneCall({
              phoneNumber: '18975150553', // 客服电话
              success: () => { console.log('拨打电话成功'); },
              fail: (err) => {
                console.log('拨打电话失败', err);
                wx.showToast({ title: '拨打失败', icon: 'none' });
              }
            });
            break;
          case 1:
            // 显示详细联系方式
            wx.showModal({
              title: '客服联系方式',
              content: '客服电话：18975150553\r\n工作时间：9:00-18:00\r\n\r\n如有问题请联系我们',
              showCancel: false,
              confirmText: '知道了'
            });
            break;
        }
      }
    });
  },
  picClick(e) {
    this.setData({ imageViewerIndex: e.detail.index, imageViewerVisible: true });
  },
  onCloseImageViewer() {
    this.setData({ imageViewerVisible: false });
  },

  // 加载评价数据
  async loadCommentData(goodsId) {
    try {
      const result = await OrderApi.getcommentByGoods({
        current: 1,
        size: 3, // 只显示前3条评价
        goodsId
      });

      // 计算平均评分
      let totalRating = 0;
      if (result.records && result.records.length > 0) {
        totalRating = result.records.reduce((sum, item) => sum + (item.rating || 0), 0);
      }
      const averageRating = result.records.length > 0 ? (totalRating / result.records.length).toFixed(1) : 0;

      this.setData({
        commentList: result.records || [],
        commentTotal: result.total || 0,
        averageRating: parseFloat(averageRating)
      });
    } catch (error) {
      console.error('加载评价数据失败:', error);
    }
  },

  // 查看全部评价
  viewAllComments() {
    const { goodsId } = this.data;
    wx.navigateTo({
      url: `/pages/comment/list/list?goodsId=${goodsId}`
    });
  },

  onLoad(options) {
    this.init(options.goodsId);

    // 设置分享菜单
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  onReady() {

  },
  onShow() {

  },
  onHide() {

  },
  onUnload() {

  },

  // 分享给好友
  onShareAppMessage() {
    const { goodsInfo } = this.data;
    return {
      title: goodsInfo.name || '商品详情',
      path: `/pages/goodsDetail/goodsDetail?goodsId=${goodsInfo.id}`,
      imageUrl: 'https://hqt.csylch.com/platform/hqt.jpg'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { goodsInfo } = this.data;
    return {
      title: goodsInfo.name || '商品详情',
      imageUrl: 'https://hqt.csylch.com/platform/hqt.jpg'
    };
  }
})