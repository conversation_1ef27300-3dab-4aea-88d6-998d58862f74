
import { request } from "~/utils/http.js"
import { isLogin, setUserInfo, setToken } from "~/utils/auth.js"

/**
 * 判断用户是否登录
 */
function checkLogin() {
  return new Promise(function (resolve, reject) {
    if (isLogin()) {
      // checkSession().then(() => {
      resolve(true);
      // }).catch(() => {
      //   reject(false);
      // });
    } else {
      reject(false);
    }
  });
}

function checkSession() {
  return new Promise(function (resolve, reject) {
    wx.checkSession({
      success: function () {
        loginByWeixin().then(res => {
          if (res.user) {
            resolve(true);
          } else {
            resolve(false);
          }
        }).catch(error => {
          resolve(false);
        })
      },
      fail: function () {
        reject(false);
      }
    })
  });
}

/**
 * 调用微信登录
 */
function loginByWeixin(phoneInfo) {
  return new Promise(function (resolve, reject) {
    return wxLogin().then((res) => {
      const params = { code: res.code };
      if (phoneInfo) {
        params.phoneInfo = JSON.stringify(phoneInfo);
      }
      requestLoginByWX(params).then(res => {
        // 存储用户信息
        setUserInfo(res.user);
        setToken(res.token);
        resolve(res);
      }).catch((err) => {
        reject(err);
      });
    }).catch((err) => {
      reject(err);
    });
  });
}

function hasRegister() {
  return new Promise(function (resolve, reject) {
    return wxLogin().then((res) => {
      requestHasRegister({ code: res.code }).then(res => {
        resolve(res);
      }).catch((err) => {
        reject(err);
      });
    }).catch((err) => {
      reject(err);
    });
  });
}

function wxLogin() {
  return new Promise(function (resolve, reject) {
    wx.login({
      success: function (res) {
        if (res.code) {
          resolve(res);
        } else {
          reject(res);
        }
      },
      fail: function (err) {
        reject(err);
      }
    });
  });
}

function requestHasRegister(params) {
  return request('/app/auth/hasRegister', params);
}
function requestLoginByWX(params) {
  return request('/app/auth/loginByWX', params);
}

function requestLoginInfo() {
  return request('/app/auth/info', {}, "GET");
}
function updateInfo(params) {
  return request('/app/auth/updateInfo', params);
}

module.exports = {
  checkSession, checkLogin, loginByWeixin, requestLoginInfo, updateInfo, hasRegister
}