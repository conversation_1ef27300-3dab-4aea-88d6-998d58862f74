// pages/comment/list/list.js
import * as OrderApi from "~/api/OrderApi.js"

Page({
  data: {
    goodsId: '',
    commentList: [],
    current: 1,
    size: 10,
    total: 0,
    loading: false,
    hasMore: true,
    activeTab: 'all',    // 当前激活的Tab
    currentFilter: 'all', // 当前筛选条件
    goodCount: 0,        // 好评数量
    mediumCount: 0,      // 中评数量
    badCount: 0,         // 差评数量
    averageRating: 0     // 平均评分
  },

  // 加载评价列表
  async loadCommentList(isRefresh = false) {
    if (this.data.loading) return;

    const { goodsId, current, size, commentList, currentFilter } = this.data;

    if (!goodsId) {
      wx.showToast({
        title: '商品信息错误',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      const params = {
        current: isRefresh ? 1 : current,
        size,
        goodsId
      };

      // 如果是最新筛选，添加排序参数
      if (currentFilter === 'latest') {
        params.orderBy = 'createTime';
        params.orderDirection = 'desc';
      }

      // 调试：打印API请求参数
      console.log('API请求参数:', params);
      console.log('当前筛选条件:', currentFilter);

      const result = await OrderApi.getcommentByGoods(params);

      // 调试：打印API返回的数据结构
      console.log('评价API返回数据:', result);
      if (result.records && result.records.length > 0) {
        console.log('第一条评价数据:', result.records[0]);
        console.log('用户名字段检查:', {
          userName: result.records[0].userName,
          userNickname: result.records[0].userNickname,
          nickName: result.records[0].nickName,
          userNickName: result.records[0].userNickName,
          userAvatar: result.records[0].userAvatar
        });
      }

      let filteredRecords = result.records || [];

      // 前端进行筛选（除了全部和最新）
      if (currentFilter !== 'all' && currentFilter !== 'latest') {
        filteredRecords = this.filterCommentsByRating(filteredRecords, currentFilter);
        console.log(`筛选${currentFilter}后的结果:`, filteredRecords.length, '条');
      }

      const newCommentList = isRefresh ? filteredRecords : commentList.concat(filteredRecords);
      const newCurrent = isRefresh ? 2 : current + 1;
      const hasMore = newCommentList.length < result.total;

      this.setData({
        commentList: newCommentList,
        current: newCurrent,
        total: result.total,
        hasMore,
        loading: false
      });

      // 如果是刷新，重新计算各类评价数量
      if (isRefresh) {
        this.calculateRatingCounts();
      }

    } catch (error) {
      console.error('加载评价列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 前端筛选评价（备选方案）
  filterCommentsByRating(comments, filter) {
    return comments.filter(comment => {
      const rating = comment.rating || 0;
      switch (filter) {
        case 'good':
          return rating >= 4 && rating <= 5;
        case 'medium':
          return rating === 3;
        case 'bad':
          return rating >= 1 && rating <= 2;
        default:
          return true;
      }
    });
  },

  // 计算各类评价数量
  async calculateRatingCounts() {
    const { goodsId } = this.data;
    try {
      // 先尝试获取所有评价进行前端统计
      const allResult = await OrderApi.getcommentByGoods({
        goodsId,
        current: 1,
        size: 1000 // 获取足够多的数据进行统计
      });

      if (allResult.records) {
        const goodCount = allResult.records.filter(item => item.rating >= 4 && item.rating <= 5).length;
        const mediumCount = allResult.records.filter(item => item.rating === 3).length;
        const badCount = allResult.records.filter(item => item.rating >= 1 && item.rating <= 2).length;

        // 计算平均评分
        let totalRating = 0;
        if (allResult.records.length > 0) {
          totalRating = allResult.records.reduce((sum, item) => sum + (item.rating || 0), 0);
        }
        const averageRating = allResult.records.length > 0 ? (totalRating / allResult.records.length).toFixed(1) : 0;

        this.setData({
          goodCount,
          mediumCount,
          badCount,
          averageRating: parseFloat(averageRating)
        });

        console.log('评价数量统计:', { goodCount, mediumCount, badCount, averageRating });
      }
    } catch (error) {
      console.error('计算评价数量失败:', error);
    }
  },

  // Tab切换事件
  onTabChange(e) {
    const { name } = e.detail;
    if (name === this.data.currentFilter) return;

    console.log('切换筛选条件:', name);

    this.setData({
      activeTab: name,
      currentFilter: name,
      commentList: [],
      current: 1,
      hasMore: true
    });

    this.loadCommentList(true);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadCommentList(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCommentList();
    }
  },

  onLoad(options) {
    const { goodsId } = options;
    if (goodsId) {
      this.setData({ goodsId });
      this.loadCommentList(true);
    }
  }
});
