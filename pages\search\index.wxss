/* pages/search/index.wxss */
.search-page {
  background-color: #ffffff;
  min-height: 100vh;
  padding-bottom: 20rpx;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 顶部搜索区域 */
.search-header {
  background-color: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  height: 40px;
  background-color:#f4f3f3;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  min-width: 0;
  box-sizing: border-box;
}

.search-btn {
  background-color: #2492F2;
  color: #ffffff;
  padding: 12rpx 30rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  white-space: nowrap;
  height: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  height: 100%;
  background: transparent;
  border: none;
  outline: none;
  font-size: 28rpx;
  color: #333333;
  min-width: 0;
  width: 100%;
}

.clear-icon {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.search-placeholder {
  color: #999999;
  font-size: 28rpx;
}

/* 搜索历史 */
.search-history {
  width: 95%;
  max-width: calc(100vw - 40rpx);
  margin: 20rpx auto;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.history-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.history-tag {
  background-color: #f5f5f5;
  color: #666666;
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
}

/* 排序筛选栏 */
.sort-bar {
  width: 95%;
  margin: 20rpx auto;
  background-color: #ffffff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.sort-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.sort-item.active {
  color: #2492F2;
  font-weight: bold;
}

.sort-item:last-child {
  flex: 1;
  padding: 0 20rpx;
}

/* 推荐标题 */
.recommend-title {
  max-width: calc(100vw - 40rpx);
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.hot-icon {
  width: 40rpx;
  height: 40rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #ea480e;
  font-style: italic;
}

/* 商品列表 */
.goods-list {
  max-width: calc(100vw - 40rpx);
  margin: 20rpx auto;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 20px;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
}

.goods-item {
  border-radius: 16rpx;
  padding: 10rpx;
  display: flex;
}

.goods-image-container {
  position: relative;
  margin-right: 20rpx;
}

.goods-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
}

.rank-badge {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #FF6B35;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.goods-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-subtitle {
  font-size: 26rpx;
  color: #666666;
  margin-top: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-stats {
  margin-top: 16rpx;
}

.sales-count {
  font-size: 24rpx;
  color: #999999;
}

.goods-price-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #FF6B35;
  font-weight: bold;
}

.price {
  font-size: 36rpx;
  color: #FF6B35;
  font-weight: bold;
}

.add-cart-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #2492F2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态 */
.empty-state {
  width: 95%;
  max-width: calc(100vw - 40rpx);
  margin: 20rpx auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-top: 30rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999999;
  margin-top: 16rpx;
}
