<!--pages/my/address.wxml-->
<wxs module="utils">
  var hidePhoneNum = function (array) { if (!array) return; var mphone = array.substring(0, 3) + '****' + array.substring(7); return mphone; }
  module.exports = { hidePhoneNum: hidePhoneNum }
</wxs>

<view class="flex-row-center" style="padding: 24rpx 30rpx;border-bottom: 2rpx dashed var(--themeColor);background-color: #FFF;" bind:tap="gotoAdd">
  <t-icon name="bill" size="20" style="color: var(--themeColor);" />
  <text style="font-size: 34rpx;margin-left: 14rpx;">新增开票信息</text>
</view>

<view style="background-color: #fff;">
  <t-swipe-cell class="swipe-out" wx:for="{{ invoiceList }}" wx:key="id">
    <view class="address-item" catch:tap="doSelect" data-item="{{item}}">
      <view class="address-item-left">
        <view class="address-item-head">
          <text wx:if="{{ item.isDefault == 1 }}" class="address-item-default">默认</text>
          <text class="address-item-name">{{ item.titleName }}</text>
        </view>
        <text class="address-item-phone">{{ item.titleType }} {{ item.invoiceType===1?"增值税普通发票（普票）":"增值税专用发票（专票）" }}</text>
      </view>
      <view class="address-item-edit" catch:tap="gotoEdit" data-item="{{item}}">
        <t-icon name="edit-2" size="20" color="var(--themeColor)" />
      </view>
    </view>
    <view slot="right" class="swipe-right-del" bind:tap="onDelete" data-item="{{item}}"> 删除 </view>
  </t-swipe-cell>
</view>