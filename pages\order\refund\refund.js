// pages/order/refund/refund.js
import * as OrderApi from "~/api/OrderApi.js"

Page({
  data: {
    orderCode: '',
    goodsInfo: null,
    applyReason: '',
    applyQuantity: 1,
    maxQuantity: 1,
    unitPrice: 0,
    applyAmount: 0,
    submitting: false
  },

  onLoad(options) {
    const { orderCode } = options;
    if (orderCode) {
      this.setData({ orderCode });
      this.loadOrderDetail(orderCode);
    }
  },

  // 加载订单详情
  async loadOrderDetail(orderCode) {
    try {
      wx.showLoading({ title: '加载中...', mask: true });
      
      const orderDetail = await OrderApi.orderDetail(orderCode);

      console.log('订单详情:', orderDetail);

      // 获取第一个商品信息（假设一个订单只有一个商品，或者只退第一个商品）
      if (orderDetail.orderItemList && orderDetail.orderItemList.length > 0) {
        const goodsInfo = orderDetail.orderItemList[0];
        const unitPrice = goodsInfo.price;
        const maxQuantity = goodsInfo.buyNum || goodsInfo.goodsNum;

        console.log('商品信息:', goodsInfo);
        console.log('商品图片:', goodsInfo.pic);

        this.setData({
          goodsInfo,
          unitPrice,
          maxQuantity,
          applyQuantity: 1,
          applyAmount: unitPrice
        });
      }
      
      wx.hideLoading();
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 退款理由输入
  onReasonChange(event) {
    this.setData({
      applyReason: event.detail
    });
  },

  // 数量变化
  onQuantityChange(event) {
    const quantity = event.detail;
    const applyAmount = (this.data.unitPrice * quantity).toFixed(2);
    
    this.setData({
      applyQuantity: quantity,
      applyAmount: parseFloat(applyAmount)
    });
  },

  // 图片加载错误处理
  onImageError(e) {
    console.log('图片加载失败:', e);
    // 可以在这里设置默认图片或其他处理
  },

  // 取消
  onCancel() {
    wx.navigateBack();
  },

  // 确定提交
  async onConfirm() {
    // 防止重复提交
    if (this.data.submitting) {
      return;
    }

    const { orderCode, applyReason, applyQuantity, applyAmount } = this.data;

    // 验证输入
    if (!applyReason.trim()) {
      wx.showToast({
        title: '请输入退款理由',
        icon: 'none'
      });
      return;
    }

    if (applyQuantity <= 0) {
      wx.showToast({
        title: '请选择退款数量',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ submitting: true });

      // 调用退款接口
      await OrderApi.createAfterSales({
        orderCode,
        serviceType: 'refund',
        applyReason: applyReason.trim(),
        applyQuantity,
        applyAmount
      });

      wx.showToast({
        title: '申请成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('申请退款失败:', error);
      wx.showToast({
        title: error.message || '申请失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  }
});
