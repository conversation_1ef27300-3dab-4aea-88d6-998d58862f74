<t-tab-bar value="{{active}}" bindchange="onChange" split="{{false}}">
  <t-tab-bar-item wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index">
    <view class="custom-tab-bar-wrapper">
      <!-- <t-icon name="{{item.icon}}" size="48rpx" /> -->
      <t-icon wx:if="{{ index == active }}" prefix="mallicon" name="{{item.icon + '-fill'}}" size="48rpx" />
      <t-icon wx:else prefix="mallicon" name="{{item.icon}}" size="48rpx" />
      <view class="text">{{ item.text }}</view>
    </view>
  </t-tab-bar-item>
</t-tab-bar>