<!--pages/order/orderDetail/orderDetail.wxml-->
<wxs module="utils">
  var hidePhoneNum = function (array) { if (!array) return; var mphone = array.substring(0, 3) + '****' + array.substring(7); return mphone; }
  var calcNum = function (list) {
    var num = 0;
    for (var i = 0; i < list.length; i++) {
      num += list[i].buyNum;
    }
    return num;
  }
  var calcPrice = function (list) {
    var price = 0;
    for (var i = 0; i < list.length; i++) {
      price += list[i].goodsItem.price * 100 * list[i].buyNum;
    }
    return price;
  }
  var mainPic = function (pic) { return pic.split(",")[0]; }
  module.exports = { hidePhoneNum: hidePhoneNum, calcNum: calcNum, calcPrice: calcPrice, mainPic: mainPic }
</wxs>

<view class="divider-line" />
<view class="flex-row-center" style="padding: 20rpx 30rpx;background-color: #fff;">
  <text class="order-item-header-no">订单号 {{orderDetail.orderCode}}</text>
  <text wx:if="{{orderDetail.orderStatus === 0}}" style="color: #FF9500;" class="order-item-header-status">待支付</text>
  <text wx:elif="{{orderDetail.orderStatus === 11}}" style="color: #3498DB;" class="order-item-header-status">待发货</text>
  <text wx:elif="{{orderDetail.orderStatus === 21}}" style="color: #F1C40F;" class="order-item-header-status">待收货</text>
  <text wx:elif="{{orderDetail.orderStatus === 50}}" style="color: #27AE60;" class="order-item-header-status">已完成</text>
  <text wx:elif="{{orderDetail.orderStatus === 1 || orderDetail.orderStatus === 12}}" style="color: #7F8C8D;" class="order-item-header-status">已取消</text>
</view>
<view class="divider-line" />
<view class="flex-row-center" style="padding: 24rpx 30rpx;border-bottom: 2rpx dashed var(--themeColor);background-color: #FFF;">
  <t-icon name="location" size="18" style="color: var(--themeColor);" />
  <view wx:if="{{address.orderCode}}" class="flex-column" style="flex: 1;padding: 0rpx 30rpx;">
    <text style="font-size: 26rpx;color: #666;">{{address.provinceName}}{{address.cityName}}{{address.countyName}}</text>
    <text style="font-size: 26rpx;color: #333;font-weight: bold;margin-top: 2rpx;">{{address.detailInfo}}</text>
    <view style="font-size: 24rpx;margin-top: 8rpx;color: #999;">{{ address.bcName }}</view>
    <view class="flex-row-center" style="margin-top: 8rpx;">
      <text style="font-size: 30rpx;color: #333;">{{address.userName}}</text>
      <text style="font-size: 28rpx;margin-left: 20rpx;color: #999;">{{utils.hidePhoneNum(address.telNumber)}}</text>
    </view>
  </view>
</view>
<view class="divider-line" />

<view class="flex-column" style="background-color: #FFF;padding: 0rpx 30rpx 10rpx 30rpx;">
  <view wx:for="{{goodsList}}" class="flex-row goods-item-clickable" style="padding: 26rpx 0 20rpx;border-bottom: 2rpx solid #EFEFEF;" wx:key="id" bindtap="navigateToGoodsDetail" data-item="{{item}}">
    <view class="goods-info__img">
      <!-- <t-image t-class="goods-info__img_v" shape="round" src="{{ utils.mainPic(item.pic) }}"  /> -->
      <image class="goods-info__img_v" src="{{ utils.mainPic(item.pic) }}" />
    </view>
    <view class="goods-info__content">
      <view class="goods-info__title">{{ item.goodsName }}</view>
      <!-- <view class="goods-info__desc">{{ item.goods.goodsDesc }}</view> -->
      <view class="goods-info__spec">
        规格：{{ item.specDesc }}
      </view>
    </view>

    <view class="goods-info__right_content">
      <view class="goods-info__price">
        <price price="{{ item.price * 100 }}" fill decimalSmaller />
      </view>
      <view class="goods-info__num">x {{ item.buyNum }}</view>
    </view>

  </view>

  <view class="flex-row-center label-layout">
    <text>下单时间</text>
    <text>{{orderDetail.createTime}}</text>
  </view>

  <block wx:if="{{orderDetail.orderDelivery.status == 2 || orderDetail.orderDelivery.status == 10}}">
    <view class="flex-row-center label-layout">
      <text>快递单号</text>
      <text>{{orderDetail.orderDelivery.deliveryCode}}</text>
    </view>
    <view class="flex-row-center label-layout">
      <text>快递公司</text>
      <text>{{orderDetail.orderDelivery.shipper}}</text>
    </view>
  </block>

  <view class="remark-class">
    <t-input value="{{item.remark||'无'}}" label="订单备注" layout="vertical" borderless="{{true}}" />
  </view>

  <view class="order-item-calc">
    <!-- <view class="order-item-calc-1">
          <text>总价 </text>
          <price price="{{item.orderPrice*100}}" fill decimalSmaller />
        </view> -->
    <!-- <view class="order-item-calc-1">
          <text>运费 </text>
          <price price="{{item.deliveryPrice*100}}" fill decimalSmaller />
        </view> -->
    <view class="order-item-calc-2">
      <text>订单总价: </text>
      <!-- <text>实付 </text> -->
      <price style="color: #fa4126;font-weight: bold;font-size: 32rpx;" price="{{orderDetail.orderPayPrice*100}}" />
    </view>
  </view>

</view>

<view class="bottom_submit_layout">
  <view wx:if="{{orderDetail.orderStatus == 0 || orderDetail.orderStatus == 11}}" data-item="{{orderDetail}}" catch:tap="doCancelOrder" class="outline-btn order-list-btn" hover-class="hover-btn">取消订单</view>
  <view wx:if="{{orderDetail.orderStatus == 0}}" data-item="{{orderDetail}}" catch:tap="doPay" style="margin-left: 30rpx;" class="red-btn order-list-btn" hover-class="hover-btn">付款</view>
  <!-- <view wx:if="{{orderDetail.orderStatus == 50}}" data-item="{{orderDetail}}" catch:tap="doRebuy" style="margin-left: 30rpx;" class="theme-btn order-list-btn" hover-class="hover-btn">再次购买</view> -->
  <view wx:if="{{orderDetail.orderStatus == 21}}" data-item="{{orderDetail}}" catch:tap="doTakeGoods" style="margin-left: 30rpx;" class="theme-btn order-list-btn" hover-class="hover-btn">确认收货</view>
  <view wx:if="{{orderDetail.orderStatus == 50 && (orderDetail.orderInvoiceList.length==0 || orderDetail.orderInvoiceList[0].status==0)}}" data-item="{{orderDetail}}" catch:tap="doTakeInvoice" style="margin-left: 30rpx;" class="outline-btn order-list-btn" hover-class="hover-btn">开具发票</view>
  <view wx:if="{{orderDetail.orderStatus == 50 && orderDetail.orderInvoiceList.length>0 && orderDetail.orderInvoiceList[0].status==1}}" data-item="{{orderDetail}}" catch:tap="doSaveInvoice" style="margin-left: 30rpx;" class="outline-btn order-list-btn" hover-class="hover-btn">下载发票</view>
</view>

<!-- 支付方式选择动作面板 -->
<van-action-sheet
  show="{{ showPaymentActionSheet }}"
  actions="{{ paymentActions }}"
  bind:select="onSelectPaymentMethod"
  bind:close="onClosePaymentActionSheet"
  title="选择支付方式"
  cancel-text="取消"
  close-on-click-action="{{ true }}"
/>

<!-- 调试按钮 -->
<!-- <view style="position: fixed; top: 100rpx; right: 20rpx; z-index: 9999;">
  <button size="mini" bindtap="testActionSheet">测试面板</button>
</view> -->

<!-- 线下支付信息遮罩层 -->
<van-overlay
  show="{{ showOfflinePaymentOverlay }}"
  bind:click="closeOfflinePaymentInfo"
  z-index="1000"
>
  <view class="offline-payment-wrapper" catch:tap="stopPropagation">
    <view class="offline-payment-content">
      <view class="offline-payment-header">
        <text class="offline-payment-title">线下支付信息</text>
        <view class="offline-payment-close" bind:tap="closeOfflinePaymentInfo">
          <text>×</text>
        </view>
      </view>

      <view class="offline-payment-body">
        <text class="offline-payment-greeting">您好！</text>

        <text class="offline-payment-description">以下是本平台的对公账户信息，烦请您妥善留存并用于公对公付款：</text>

        <view class="payment-info-item">
          <text class="info-label">账户名称:</text>
          <text class="info-value">长沙耀楼产业互联科技服务有限公司</text>
        </view>

        <view class="payment-info-item">
          <text class="info-label">账户号码:</text>
          <text class="info-value">731912140110006</text>
        </view>

        <view class="payment-info-item">
          <text class="info-label">开户银行:</text>
          <text class="info-value">招商银行股份有限公司长沙晓园支行</text>
        </view>

        <view class="payment-info-item">
          <text class="info-label">纳税识别号：</text>
          <text class="info-value">91430104MAEBFJ0G7C</text>
        </view>

        <view class="payment-info-item">
          <text class="info-label">公司地址:</text>
          <text class="info-value">湖南省长沙市岳麓区岳麓街道溁左路中南大学科技园研发总部 6 栋 322 房</text>
        </view>

        <text class="offline-payment-notice">在您完成公对公付款后，请及时保留好付款凭证。平台会在第一时间对付款信息进行核实，确认无误后，将迅速为您处理订单，全力保障您的业务顺利推进。</text>

        <text class="offline-payment-thanks">如有任何疑问，欢迎随时与我们联系。</text>
        <text class="offline-payment-thanks">感谢您的信任与支持 🌹</text>
      </view>

      <view class="offline-payment-footer">
        <view class="copy-btn" bind:tap="copyPaymentInfo">
          <text>一键复制</text>
        </view>
        <view class="confirm-btn" bind:tap="closeOfflinePaymentInfo">
          <text>我知道了</text>
        </view>
      </view>
    </view>
  </view>
</van-overlay>