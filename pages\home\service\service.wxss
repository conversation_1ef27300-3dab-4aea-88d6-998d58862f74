/* pages/home/<USER>/

page {
  background-color: var(--pageBgColor);
  height: 100vh;
}

/* 底部安全区域 */
.safe-area-bottom {
  padding-bottom: calc(var(--tabbarHeight) + env(safe-area-inset-bottom));
}



page .round-image {
  border-radius: 12rpx;
}

.side-bar-wrapper {
  display: flex;
  height: calc(100vh - 88px - var(--tabbarHeight) - env(safe-area-inset-bottom));
  overflow: hidden;
  background-color: #ffffff;
}

.side-bar-wrapper .content {
  flex: 1;
  background-color: #f8f9fa;
  margin-left: 2rpx;
}

.side-bar-wrapper .section {
  padding: 24rpx 16rpx;
  box-sizing: border-box;
  height: 100%;
}

.side-bar-wrapper .title {
  padding: 20rpx 20rpx 20rpx 32rpx;
  margin: 0 0 16rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
}

.side-bar-wrapper .title::before {
  content: '';
  position: absolute;
  left: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(135deg, var(--themeColor), #3486eb);
  border-radius: 3rpx;
}

.side-bar-wrapper .image {
  width: 140rpx;
  height: 140rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
}

.side-bar-wrapper .item-leyout {
  margin: 0 0 16rpx 0;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.side-bar-wrapper .item-leyout:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section .item-right {
  margin-left: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 140rpx;
}

.section .item-name {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}

.section .item-desc {
  flex: 1;
  width: 100%;
}

.section .item-price1 {
  font-size: 22rpx;
  color: #ff4757;
  font-weight: 600;
}

.section .item-price2 {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: bold;
  margin-left: 4rpx;
}

.goods-item-add {
  color: var(--themeColor);
  
  width:40rpx;
  height: 40rpx;
  padding: 10rpx;
}

/* 空状态优化 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  margin: 60rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  line-height: 1.4;
}

/* 滚动条隐藏 */
.section::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 简单的动画效果 */
.side-bar-wrapper .item-leyout {
  animation: fadeInUp 0.3s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .side-bar-wrapper .section {
    padding: 16rpx 12rpx;
  }

  .side-bar-wrapper .item-leyout {
    padding: 16rpx;
  }

  .side-bar-wrapper .image {
    width: 120rpx;
    height: 120rpx;
  }

  .section .item-right {
    margin-left: 16rpx;
    min-height: 120rpx;
  }

  .section .item-name {
    font-size: 28rpx;
  }

  .section .item-price2 {
    font-size: 30rpx;
  }

  .goods-item-add {
    padding: 8rpx;
  }
}