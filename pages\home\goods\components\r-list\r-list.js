// pages/goods/components/r-list/r-list.js
Component({
  properties: {
    currentType: { type: Object },
    goodsList: { type: Array, value: [] },
    cartList: { type: Array, value: [] },
  },
  data: {
    scrollTop: 0,
  },
  methods: {
    calNum(id) {
      console.log(id);
      console.log(this.data.goodsList);
      return 1;
    },
    onScroll(e) {
      // const { scrollTop } = e.detail;
      // const threshold = 50; // 下一个标题与顶部的距离

      // if (scrollTop < threshold) {
      //   this.setData({ sideBarIndex: 0 });
      //   return;
      // }

      // const index = this.offsetTopList.findIndex((top) => top > scrollTop && top - scrollTop <= threshold);

      // if (index > -1) {
      //   this.setData({ sideBarIndex: index });
      // }
    },
  }
})