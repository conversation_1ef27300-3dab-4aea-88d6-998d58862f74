/* pages/my/address.wxss */
page {
  background-color: var(--pageBgColor);
  height: calc(100vh - env(safe-area-inset-bottom));
}

.address-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1rpx solid #EFEFEF;
  padding: 20rpx 30rpx;
}

.address-item-left {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.address-item-head {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.address-item-default {
  background-color: var(--themeColor);
  border-radius: 999rpx;
  color: #FFF;
  font-size: 26rpx;
  padding: 4rpx 14rpx;
  margin-right: 14rpx;
}

.address-item-name {
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}

.address-item-phone {
  color: #333;
  font-size: 32rpx;
  margin-left: 20rpx;
}

.address-item-info {
  margin-top: 6rpx;
  color: #666;
  font-size: 30rpx;
}

.address-item-info2 {
  margin-top: 14rpx;
  color: #999;
  font-size: 24rpx;
}

.address-item-edit {
  padding: 20rpx;
}

.swipe-out {
  overflow: hidden; /* 确保不会因为其他样式被剪裁 */
}

.swipe-right-del {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 144rpx;
  height: 100%;
  background-color: #fa4126;
  color: #fff;
  font-size: 28rpx;
  line-height: 40rpx;
}