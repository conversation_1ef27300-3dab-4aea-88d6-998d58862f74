import Toast from 'tdesign-miniprogram/toast';

Component({
  isSpecsTap: false, // 标记本次点击事件是否因为点击specs触发（由于底层goods-card组件没有catch specs点击事件，只能在此处加状态来避免点击specs时触发跳转商品详情）
  properties: {
    cartInfo: {
      type: Object, observer(cartInfo) {
        if (cartInfo.specStatus !== 0) {
          cartInfo.goods.specStr = "已失效";
        } else if (cartInfo.goods.specType === 1) {
          cartInfo.goods.specStr = "默认";
        } else if (cartInfo.goods.specType === 2) {
          cartInfo.goods.specStr = cartInfo.specDesc;
        }
        this.setData({ _cartInfo: cartInfo });
      }
    }
  },
  data: {
    _cartInfo: {},
  },
  methods: {
    toGoodsDetail(e) {
      const { goods } = e.currentTarget.dataset;
      wx.navigateTo({ url: '/pages/goodsDetail/goodsDetail?goodsId=' + goods.id });
    },
    // 选中商品
    selectGoods(e) {
      this.setData({ "_cartInfo.selected": !this.data._cartInfo.selected });
      this.triggerEvent('handleSelectGoods', { goods: this.data._cartInfo, selected: this.data._cartInfo.selected });
    },
    // 删除商品
    deleteGoods(e) {
      this.triggerEvent('handleDeleteGoods', { goods: this.data._cartInfo });
    },
    // 变更数量
    changeStepper(e) {
      const { value } = e.detail;
      this.changeQuantity(value);
    },
    // 手动输入数量
    input(e) {
      const { value } = e.detail;
      this.changeQuantity(value);
    },
    // 变更数量
    changeQuantity(num) {
      this.setData({ "_cartInfo.goodsNum": num });
      this.triggerEvent('handleChangeNum', { goods: this.data._cartInfo, num: num });
    },
    // 小与最小值
    overlimit(e) {
      const text = e.detail.type === 'minus' ? '该商品数量不能减少了哦' : '同一商品最多购买999件';
      Toast({ context: this, selector: '#t-toast', message: text });
    },
    goGoodsDetail(e) {
      if (this.isSpecsTap) {
        this.isSpecsTap = false;
        return;
      }
      const { goods } = e.currentTarget.dataset;
      this.triggerEvent('goodsclick', { goods });
    },

  }
});
