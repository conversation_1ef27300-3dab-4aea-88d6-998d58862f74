Component({
  properties: {
    // 导航栏标题
    title: {
      type: String,
      value: '耀楼惠企通'
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: false
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      value: '#ffffff'
    },
    // 文字颜色
    textColor: {
      type: String,
      value: '#1a1a1a'
    },
    // 是否显示楼宇选择器
    showBuildingSelector: {
      type: Boolean,
      value: false
    },
    // 当前楼宇名称
    buildingName: {
      type: String,
      value: ''
    },
    // 当前楼宇距离
    buildingDistance: {
      type: String,
      value: ''
    }
  },

  data: {
    statusBarHeight: 0,
    navBarHeight: 0
  },

  lifetimes: {
    attached() {
      this.setNavBarInfo();
    }
  },

  observers: {
    'showBuildingSelector': function(showBuildingSelector) {
      this.setNavBarInfo();
    }
  },

  methods: {
    // 设置导航栏信息
    setNavBarInfo() {
      const windowInfo = wx.getWindowInfo();
      const statusBarHeight = windowInfo.statusBarHeight;
      const baseHeight = 44; // 基础导航栏高度
      const buildingSelectorHeight = this.data.showBuildingSelector ? 44 : 0; // 楼宇选择器高度
      const navBarHeight = statusBarHeight + baseHeight + buildingSelectorHeight - 12;

      this.setData({
        statusBarHeight,
        navBarHeight
      });

      // 通知父组件导航栏高度
      this.triggerEvent('navBarHeightChange', {
        navBarHeight,
        statusBarHeight
      });
    },

    // 返回按钮点击事件
    onBack() {
      // 先触发自定义事件，让父组件处理
      const result = this.triggerEvent('back');

      // 如果父组件没有处理，则执行默认返回操作
      if (!result) {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          wx.navigateBack();
        } else {
          // 如果是首页，可以跳转到指定页面或者不做操作
          wx.switchTab({
            url: '/pages/home/<USER>/index'
          });
        }
      }
    },

    // 楼宇选择器点击事件
    onBuildingSelectorTap() {
      this.triggerEvent('buildingSelectorTap');
    }
  }
});
