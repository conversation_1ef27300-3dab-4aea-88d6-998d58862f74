// components/audit-result-popup/audit-result-popup.js
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    auditResult: {
      type: String,
      value: '' // 'agree' | 'disagree'
    },
    auditRemark: {
      type: String,
      value: ''
    },
    auditTime: {
      type: String,
      value: ''
    },
    orderCode: {
      type: String,
      value: ''
    }
  },

  data: {
    isClosing: false
  },

  methods: {
    onVisibleChange(e) {
      const { visible } = e.detail;
      if (!visible && !this.data.isClosing) {
        this.setData({ isClosing: true });
        this.triggerEvent('close');
        // 重置关闭状态
        setTimeout(() => {
          this.setData({ isClosing: false });
        }, 100);
      }
    },

    onConfirm() {
      if (!this.data.isClosing) {
        this.setData({ isClosing: true });
        this.triggerEvent('close');
        // 重置关闭状态
        setTimeout(() => {
          this.setData({ isClosing: false });
        }, 100);
      }
    },

    onRetry() {
      if (!this.data.isClosing) {
        this.setData({ isClosing: true });
        this.triggerEvent('retry');
        // 重置关闭状态
        setTimeout(() => {
          this.setData({ isClosing: false });
        }, 100);
      }
    }
  }
});
