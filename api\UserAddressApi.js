
import { request } from "~/utils/http.js"

function userAddressList() {
  return request('/app/userAddress/list', { current: 1, size: -1 });
}
function userAddressAdd(params) {
  return request('/app/userAddress/add', { ...params });
}
function userAddressUpdate(params) {
  return request('/app/userAddress/update', { ...params });
}
function userAddressInfo(id) {
  return request('/app/userAddress/info', { id });
}
function userAddressDelete(id) {
  return request('/app/userAddress/delete', { id });
}

module.exports = {
  userAddressList, userAddressAdd, userAddressUpdate, userAddressInfo, userAddressDelete
}