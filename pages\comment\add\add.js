// pages/comment/add/add.js
import * as OrderApi from "~/api/OrderApi.js"

Page({
  data: {
    orderCode: '',
    goodsInfo: {},
    rating: 0, // 初始值改为0，让用户主动选择
    content: '',
    submitLoading: false
  },

  // 星级评分改变
  onRatingChange(e) {
    const newRating = e.detail || e.detail.value || e;
    console.log('评分改变:', newRating);

    this.setData({
      rating: newRating
    });
  },

  // 评论内容输入
  onContentInput(e) {
    this.setData({
      content: e.detail.value
    });
  },

  // 提交评价
  async submitComment() {
    const { orderCode, rating, content, submitLoading } = this.data;
    
    if (submitLoading) return;

    if (rating === 0) {
      wx.showToast({
        title: '请选择评分',
        icon: 'none'
      });
      return;
    }

    if (!content.trim()) {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitLoading: true });
    wx.showLoading({ title: '提交中...', mask: true });

    try {
      await OrderApi.addOrderComment({
        orderCode,
        rating,
        content: content.trim()
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '评价成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      
    } catch (error) {
      console.error('提交评价失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitLoading: false });
    }
  },

  onLoad(options) {
    const { orderCode, goodsInfo } = options;
    
    if (orderCode) {
      this.setData({ orderCode });
    }
    
    if (goodsInfo) {
      try {
        const parsedGoodsInfo = JSON.parse(decodeURIComponent(goodsInfo));
        this.setData({ goodsInfo: parsedGoodsInfo });
      } catch (error) {
        console.error('解析商品信息失败:', error);
      }
    }
  }
});
