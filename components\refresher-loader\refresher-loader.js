// components/refresher-loader/refresher-loader.js
const defaultSize = 10;
Component({
  externalClasses: ['wr-class'],
  properties: {
    dataList: {
      type: Array, value: [], observer(data) {
        const noData = data.length < this.data.current * this.data.size;
        this.setData({ noData });
      }
    },
  },
  data: {
    isLoading: false, // 是否正在加载更多
    isRefreshing: false, // 是否正在刷新
    current: 1,
    size: defaultSize,
    totalPages: 0,
    noData: false, // 没有更多数据了
  },
  methods: {
    handleRefresh() {
      if (this.dataList) {
        this.dataList.length = 0;
      }
      this.setData({ current: 1 });
      this.triggerEvent('onRefresh', { isRefresh: true, current: this.data.current, size: this.data.size });
    },
    loadMore() {
      if (!this.data.noData) {
        this.setData({ current: this.data.current + 1 });
        this.triggerEvent('onRefresh', { isRefresh: false, current: this.data.current, size: this.data.size });
      }
    },
    complete({ total }) {
      const noData = total <= this.data.current * this.data.size;
      this.setData({ noData, isRefreshing: false });
    },
    completeError() {
      this.setData({ isRefreshing: false });
    },
    doRefresh() {
      this.setData({ isRefreshing: true });
      // this.handleRefresh();
    },
  }
})