<wxs src="./cart-goods.wxs" module="handlePromotion" />
<wxs src="./utils.wxs" module="utils" />

<view class="goods-item">
  <swipeout right-width="{{ 70 }}">
    <view class="goods-item-info">
      <view wx:if="{{cartInfo.specStatus === 0}}" class="check-wrap" catch:tap="selectGoods" data-goods="{{cartInfo.goods}}" bind:tap="toGoodsDetail">
        <t-icon size="40rpx" color="{{_cartInfo.selected ? 'var(--themeColor)' : '#BBBBBB'}}" name="{{_cartInfo.selected ? 'check-circle-filled' : 'circle'}}" />
      </view>
      <view wx:else class="check-wrap" data-goods="{{cartInfo.goods}}" bind:tap="toGoodsDetail">
        <t-icon size="40rpx" color="#CCCCCC" name="minus-circle" />
      </view>

      <view class="goods-sku-info">
        <view class="goods-sku-info__thumb" data-goods="{{cartInfo.goods}}" bind:tap="toGoodsDetail">
          <!-- <t-image t-class="goods-sku-info__thumb-com" shape="round" src="{{ _cartInfo.goodsItem.pic }}" lazy /> -->
          <image class="goods-sku-info__thumb-com" src="{{ _cartInfo.goodsItem.pic }}" />
        </view>
        <view class="goods-sku-info__body">
          <view class="goods-sku-info__long_content" data-goods="{{cartInfo.goods}}" bind:tap="toGoodsDetail">
            <view class="goods-sku-info__title">{{ _cartInfo.goods.name }}</view>
            <view class="goods-sku-info__desc">规格：{{ _cartInfo.goods.specStr }}</view>
            <!-- <view class="goods-sku-info__desc">{{ _cartInfo.goods.goodsDesc }}</view> -->
          </view>
          <view wx:if="{{cartInfo.specStatus === 0}}" class="goods-sku-info__short_content">
            <view class="goods-sku-info__price">
              <price price="{{ _cartInfo.goodsItem.price * 100 }}" fill decimalSmaller />
            </view>
            <t-stepper classname="stepper-info" value="{{_cartInfo.goodsNum}}" min="{{1}}" max="{{999}}" catchchange="changeStepper" catchblur="input" catchoverlimit="overlimit" theme="filled" />
          </view>
        </view>
      </view>
    </view>
    <view slot="right" class="swiper-right-del" bind:tap="deleteGoods">删除</view>
  </swipeout>
  <t-toast id="t-toast" />
</view>