let invoicePromise = [];

/** 获取一个地址选择Promise */
export const getInvoicePromise = () => {
  let resolver;
  let rejecter;
  const nextPromise = new Promise((resolve, reject) => {
    resolver = resolve;
    rejecter = reject;
  });

  invoicePromise.push({ resolver, rejecter });

  return nextPromise;
};

/** 用户选择了一个地址 */
export const resolveInvoice = (invoice) => {
  const allInvoice = [...invoicePromise];
  invoicePromise = [];

  allInvoice.forEach(({ resolver }) => resolver(invoice));
};

/** 用户没有选择任何地址只是返回上一页了 */
export const rejectInvoice = () => {
  const allInvoice = [...invoicePromise];
  invoicePromise = [];

  allInvoice.forEach(({ rejecter }) => rejecter(new Error('cancel')));
};
