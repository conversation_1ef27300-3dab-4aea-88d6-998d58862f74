/* pages/order/refund/refund.wxss */
.refund-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120rpx);
  padding-bottom: 120rpx;
}

/* 订单信息区域 */
.order-info-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.order-number {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.goods-info {
  display: flex;
  align-items: center;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  border: 1rpx solid #e5e5e5;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.goods-spec {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.goods-price {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

/* 退款理由区域 */
.reason-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 退款数量区域 */
.quantity-section {
  display: flex;
  flex-direction: row;
  background-color: #fff;
  margin-bottom: 20rpx;
  justify-content: space-between;
}

.quantity-row {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
}

/* 退款金额区域 */
.amount-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.amount-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
}

.amount-label {
  font-size: 33rpx;
  font-weight: 500;
  color: #333;
}

.amount-value {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: bold;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.refund-cancel-btn,
.refund-confirm-btn {
  height: 70rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  flex: 1;
  margin: 0 10rpx;
}

.refund-cancel-btn {
  background: linear-gradient(90deg, rgb(153, 153, 153) 0%, rgb(187, 187, 187) 100%);
  border-radius: 20rpx 0 0 20rpx;
  color: #fff;
}

.refund-confirm-btn {
  background: linear-gradient(90deg, rgb(19, 115, 231) 0%, rgb(81, 152, 238) 100%);
  border-radius: 0rpx 20rpx 20rpx 0rpx;
  color: #fff;
}

.refund-confirm-btn.disabled {
  background: linear-gradient(90deg, rgb(204, 204, 204) 0%, rgb(221, 221, 221) 100%);
  color: #999;
}

/* Vant组件样式覆盖 */
.van-field {
  padding: 20rpx 30rpx !important;
}

.van-field__control {
  min-height: 120rpx !important;
}

.van-stepper {
  border: 1rpx solid #e5e5e5 !important;
  border-radius: 8rpx !important;
}
