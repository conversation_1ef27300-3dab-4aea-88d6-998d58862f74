<!--pages/home/<USER>
<!-- 自定义导航栏 -->
<custom-navbar
  title="耀楼惠企通"
  show-back="{{false}}"
  background-color="#2492F2"
  text-color="#ffffff"
  show-building-selector="{{true}}"
  building-name="{{currentBuildingName}}"
  building-distance="{{currentBuildingDistance}}"
  bind:buildingSelectorTap="onBuildingSelectorTap"
/>

<!-- 安卓系统顶部透明占位区域 -->
<view class="android-top-spacer" wx:if="{{isAndroid}}"></view>

<scroll-view scroll-y style="height: calc(100vh - 31px - 180rpx - var(--tabbarHeight) - env(safe-area-inset-bottom));">

  <view class="flex-column">

    <view class="flex-column swiper-container">
      <!-- 搜索框 -->
      <view class="search-container">
        <view class="search-box" bind:tap="gotoSearch">
          <t-icon name="search" size="32rpx" color="#999999" />
          <text class="search-placeholder">搜索商品或服务</text>
        </view>
      </view>

      <view class="swiper-background">
        <t-swiper class="custom-swiper" style="position: relative;flex: 1;display: block;margin: 20rpx;" height="300rpx" list="{{swiperList}}" navigation="{{ { type: 'dots-bar' } }}" />
      </view>
      <!-- <image mode="heightFix" style="height: 160rpx;margin: 0 auto;margin-top: 30rpx;" src="https://ui.yunchencloud.cn/img/banner.png" /> -->
      
      <view class="menu-container">
        <view class="flex-column-center menu-container-item" wx:for="{{menuList}}" wx:key="id" data-item="{{item}}" bind:tap="toPage">
          <image class="menu-container-img" mode="heightFix" src="{{item.pic}}" />
          <text class="menu-container-txt">{{item.name}}</text>
        </view>
        <view class="flex-column-center menu-container-item" bind:tap="toService">
          <image class="menu-container-img" mode="heightFix" src="/static/images/menu_all.png" />
          <text class="menu-container-txt">全部</text>
        </view>
      </view>
      
    </view>


    <view class="section-title-container">
      <view class="section-title-wrapper">
        <view class="title-divider left-divider"></view>
        <view class="section-title">精选推荐</view>
        <view class="title-divider right-divider"></view>
      </view>
    </view>

    <index-goods goodsList="{{goodsList}}" bind:toAddGoods="toAddGoods"></index-goods>
    <!-- <index-goods></index-goods> -->

  </view>

</scroll-view>
<goods-specs-popup id="goodsSpecsPopup" bind:doBuyNow="doBuyNow" bind:doAddCart="doAddCart" goodsInfo="{{goodsInfo}}" show="{{isSpuSelectPopupShow}}" bind:closeSpecsPopup="handlePopupHide">
</goods-specs-popup>

<!-- 悬浮客服按钮 -->
<floating-kefu init-x="600" init-y="700" />