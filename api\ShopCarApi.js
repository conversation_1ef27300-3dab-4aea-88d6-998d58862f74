
import { request } from "~/utils/http.js"

function shopCarList(params) {
  return request('/app/shopCar/list', { ...params, status: 0 });
}

function shopCarAdd(goodsId, goodsItemId, buyNum) {
  return request('/app/shopCar/add', { goodsId, goodsItemId, buyNum });
}
function shopCarSubtract(goodsId, goodsItemId) {
  return request('/app/shopCar/subtract', { goodsId, goodsItemId });
}
function shopCarUpdateNum(id, goodsNum) {
  return request('/app/shopCar/updateNum', { id, goodsNum });
}
function shopCarUpdateSelected(id, selected) {
  return request('/app/shopCar/updateSelected', { id, selected });
}
function shopCarUpdateSelectedAll(selected) {
  return request('/app/shopCar/updateSelectedAll', { selected });
}
function shopCarDelete(id) {
  return request('/app/shopCar/delete', { id });
}
function shopDeleteAll() {
  return request('/app/shopCar/deleteAll', {});
}

module.exports = {
  shopCarList, shopCarAdd, shopCarSubtract, shopCarUpdateNum, shopCarUpdateSelected, shopCarUpdateSelectedAll, shopCarDelete, shopDeleteAll
}