.user-center-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.user-center-card:active {
  transform: scale(0.98);
}

.user-center-card__header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  width: 100%;
}

.user-center-card__header__avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-center-card__header__avatar .t-avatar {
  width: 100% !important;
  height: 100% !important;
}

.user-center-card__header__avatar .t-avatar__wrapper {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* .user-center-card__header__avatar image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
} */

.user-center-card__header__name {
  font-size: 36rpx;
  color: #1a1a1a;
  font-weight: bold;
  margin-left: 24rpx;
  margin-bottom: 8rpx;
}

.user-center-card__header__mobile {
  font-size: 26rpx;
  color: #666666;
  margin-left: 24rpx;
  background: rgba(102, 126, 234, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.user-center-card__header__transparent {
  position: absolute;
  left: 0;
  top: 0;
  background-color: transparent;
  height: 100%;
  width: 100%;
  border-radius: 20rpx;
}

/* 动画效果 */
@keyframes avatarPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.user-center-card__header__avatar {
  animation: avatarPulse 3s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .user-center-card {
    padding: 28rpx;
  }

  .user-center-card__header__avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
  }

  .user-center-card__header__name {
    font-size: 32rpx;
    margin-left: 20rpx;
  }

  .user-center-card__header__mobile {
    font-size: 24rpx;
    margin-left: 20rpx;
    padding: 6rpx 12rpx;
  }
}