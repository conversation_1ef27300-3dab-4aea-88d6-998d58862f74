<!--components/comment-detail-popup/comment-detail-popup.wxml-->
<wxs module="utils">
  function getRatingText(rating) {
    switch (rating) {
      case 1: return '很差';
      case 2: return '较差';
      case 3: return '一般';
      case 4: return '满意';
      case 5: return '非常满意';
      default: return '未知';
    }
  }
  module.exports = { getRatingText: getRatingText };
</wxs>

<view class="comment-popup-overlay {{show ? 'show' : ''}}" bind:tap="onClose">
  <view class="comment-popup-content" bind:tap="preventBubble">
    <!-- 头部 -->
    <view class="popup-header">
      <view class="popup-title">评价详情</view>
      <view class="popup-close" bind:tap="onClose">
        <text class="close-icon">×</text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="goods-info" wx:if="{{orderInfo.orderItemList && orderInfo.orderItemList.length > 0}}">
      <image 
        class="goods-image" 
        src="{{orderInfo.orderItemList[0].pic}}" 
        mode="aspectFill" 
      />
      <view class="goods-details">
        <view class="goods-name">{{orderInfo.orderItemList[0].goodsName}}</view>
        <view class="goods-spec">规格: {{orderInfo.orderItemList[0].specDesc || '默认'}}</view>
      </view>
    </view>

    <!-- 评价内容 -->
    <view class="comment-content">
      <!-- 评分 -->
      <view class="rating-section">
        <van-rate
          value="{{comment.rating}}"
          size="24"
          color="#2492F2"
          void-color="#E5E5E5"
          gutter="4"
          readonly="{{true}}"
          allow-half="{{false}}"
        />
        <text class="rating-text">{{utils.getRatingText(comment.rating)}}</text>
      </view>

      <!-- 评价文字 -->
      <view class="content-section">
        <view class="content-text">{{comment.content}}</view>
      </view>

      <!-- 评价时间 -->
      <view class="time-section">
        <view class="time-text">{{comment.createTime}}</view>
      </view>
    </view>

    <!-- 确定按钮 -->
    <view class="popup-footer">
      <button class="confirm-btn" bind:tap="onClose">确定</button>
    </view>
  </view>
</view>
