let userInfo, token;

function isLogin() {
  return userInfo && token;
}

function setUserInfo(_userInfo) {
  userInfo = _userInfo;
  wx.setStorageSync('userInfo', _userInfo);
}
function setToken(_token) {
  token = _token;
  wx.setStorageSync('token', _token);
}

function getUserInfo() {
  if (!userInfo) {
    userInfo = wx.getStorageSync('userInfo')
  }
  return userInfo;
}

function getToken() {
  if (!token) {
    token = wx.getStorageSync('token')
  }
  return token;
}

function clearAuth() {
  userInfo = undefined;
  token = undefined;
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('token');
}

module.exports = {
  isLogin, setUserInfo, setToken, getUserInfo, getToken, clearAuth
}