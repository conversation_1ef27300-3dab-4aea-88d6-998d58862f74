// pages/my/address/edit/editAddress.js
import Toast from 'tdesign-miniprogram/toast/index';
import InvoiceApi from '~/api/InvoiceApi';

Page({
  data: {
    form: {
      id: '',
      titleType: '企业',
      invoiceType: 0,
      titleName: '',
      taxId: '',
      registeredAddress: '',
      registeredPhone: '',
      bankName: '',
      bankAccount: '',
      receiveEmail: '',
      isDefault: 0
    },
    submitActive: false
  },
  hasSava: false,
  privateData: { verifyTips: '' },
  onLoad(options) {
    if (options.id) {
      this.setData({ "form.id": options.id });
      this.init(true);
    }
  },
  onShow() {
  },
  async init() {
    try {
      const result = await InvoiceApi.invoiceInfo(this.data.form.id);
      this.setData({ form: result, submitActive: true });
    } catch (error) {
      console.error('err:', error);
    }
  },
  formSubmit() {
    const { submitActive } = this.data;
    if (!submitActive) {
      Toast({ context: this, selector: '#t-toast', message: this.privateData.verifyTips, icon: '', duration: 1000 });
      return;
    }
    const { form } = this.data;
    this.hasSava = true;
    wx.showLoading({ title: '正在保存...', mask: true });
    if (this.data.form.id) {
      InvoiceApi.invoiceUpdate(form).then(res => {
        this.hasSava = false;
        console.log(res);
        wx.hideLoading();
        wx.navigateBack();
      }).catch(error => {
        this.hasSava = false;
        console.log(error);
        wx.hideLoading();
      });
    } else {
      InvoiceApi.invoiceAdd(form).then(res => {
        this.hasSava = false;
        console.log(res);
        wx.hideLoading();
        wx.navigateBack();
      }).catch(error => {
        this.hasSava = false;
        console.log(error);
        wx.hideLoading();
      });
    }
  },
  onTitleTypeChange(e) {
    const { value = '' } = e.detail;
    this.setData({ "form.titleType": value, "form.invoiceType": 0 });
  },
  onInvoiceTypeChange(e) {
    const { value = '' } = e.detail;
    this.setData({ "form.invoiceType": value });
  },
  onInputValue(e) {
    const { item } = e.currentTarget.dataset;
    const { value = '' } = e.detail;
    this.setData({ [`form.${item}`]: value },
      () => {
        const { isLegal, tips } = this.onVerifyInputLegal();
        this.setData({ submitActive: isLegal });
        this.privateData.verifyTips = tips;
      },
    );
  },
  onCheckDefaultAddress({ detail }) {
    const { value } = detail;
    this.setData({ 'form.isDefault': value });
  },
  onVerifyInputLegal() {
    const { titleType, invoiceType, titleName, taxId, registeredAddress,
      registeredPhone, bankName, bankAccount, receiveEmail, } = this.data.form;

    if (!titleType || !titleType.trim()) { return { isLegal: false, tips: '请选择抬头类型' }; }
    if (invoiceType !== 1 && invoiceType !== 2) { return { isLegal: false, tips: '请选择发票类型' }; }
    if (!titleName || !titleName.trim()) { return { isLegal: false, tips: '请填写发票抬头' }; }
    if (titleType === '企业') {
      if (!taxId || !taxId.trim()) { return { isLegal: false, tips: '请填写纳税人识别号' }; }
      if (invoiceType === 2) {
        if (!registeredAddress || !registeredAddress.trim()) { return { isLegal: false, tips: '请填写企业注册地址' }; }
        if (!registeredPhone || !registeredPhone.trim()) { return { isLegal: false, tips: '请填写企业注册电话' }; }
        if (!bankName || !bankName.trim()) { return { isLegal: false, tips: '请填写基本开户银行' }; }
        if (!bankAccount || !bankAccount.trim()) { return { isLegal: false, tips: '请填写基本开户账号' }; }
      }
    }

    return { isLegal: true, tips: '添加成功' };
  }
})