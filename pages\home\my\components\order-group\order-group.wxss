.order-group {
  background-color: transparent;
  border-radius: 0;
  margin: 0;
  padding: 32rpx;
}

.order-group__head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 32rpx;
}

.order-group__head .order-group__head_title {
  font-size: 32rpx;
  color: #1a1a1a;
  font-weight: 600;
  position: relative;
  padding-left: 20rpx;
  flex: 1 1 auto;
}

.order-group__head .order-group__head_title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(135deg, var(--themeColor), #3486eb);
  border-radius: 3rpx;
}

.order-group__head .order-group__head_right {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.order-group__head .order-group__head_right:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.order-group__head .order-group__head_right .order-group__head_right_txt {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
  padding-left: 0;
  margin-right: 8rpx;
  height: 100%;
}

.order-group__head .order-group__head_right .order-group__head_right_txt::before {
  display: none;
}

.order-group__content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0;
}

.order-group__item {
  text-align: center;
  flex: 1;
  padding: 24rpx 16rpx;
  transition: all 0.3s ease;
  border-radius: 16rpx;
  position: relative;
}

.order-group__item:active {
  background: #f8f9fa;
  transform: scale(0.95);
}

.order-group__item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.order-group__item:active::before {
  width: 120rpx;
  height: 120rpx;
}

.order-group__item__title {
  margin-top: 16rpx !important;
  font-size: 24rpx !important;
  color: #666666 !important;
  font-weight: 500;
}

/* 图标容器优化 */
.order-group__item .t-badge {
  position: relative;
  z-index: 2;
}

/* 动画效果 */
@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }

  50% {
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.order-group__item {
  animation: bounceIn 0.6s ease forwards;
}

.order-group__item:nth-child(1) {
  animation-delay: 0.1s;
}

.order-group__item:nth-child(2) {
  animation-delay: 0.2s;
}

.order-group__item:nth-child(3) {
  animation-delay: 0.3s;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .order-group {
    padding: 28rpx;
  }

  .order-group__head {
    margin-bottom: 28rpx;
  }

  .order-group__item {
    padding: 20rpx 12rpx;
  }

  .order-group__item__title {
    font-size: 22rpx !important;
  }
}