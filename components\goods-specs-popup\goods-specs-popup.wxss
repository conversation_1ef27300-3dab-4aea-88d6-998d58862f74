/* components/goods-specs-popup/goods-specs-popup.wxss */
@import "/style/base.wxss";

.popup-container {
  background-color: #ffffff;
  position: relative;
  z-index: 100;
  border-radius: 16rpx 16rpx 0 0;
  padding-bottom: 20rpx;
  /* padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx); */
}

.popup-container .popup-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  z-index: 9;
  color: #999999;
}

.popup-sku__goods-price1 {
  font-size: 26rpx;
  color: #df3c39;
}

.popup-sku__goods-price2 {
  font-size: 36rpx;
  color: #df3c39;
  margin-left: 6rpx;
  font-weight: bold;
}

.popup-sku-header__img {
  width: 176rpx;
  height: 176rpx;
  border-radius: 8rpx;
  background: #d8d8d8;
  margin-right: 24rpx;
}

.popup-sku-header__goods-info {
  position: relative;
  width: 500rpx;
}

.popup-sku-header__goods-info .popup-sku__goods-name {
  font-size: 28rpx;
  line-height: 40rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  white-space: normal;
  overflow: hidden;
  width: 430rpx;
  text-overflow: ellipsis;
  margin-bottom: 26rpx;
}

.popup-sku-header__goods-info .popup-sku__selected-spec {
  margin-top: 16rpx;
  color: #333;
  font-size: 26rpx;
  line-height: 36rpx;
}

.popup-sku-body {
  margin: 0 30rpx;
  max-height: 600rpx;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.popup-sku-body .popup-sku-group-container .popup-sku-row {
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.popup-sku-body .popup-sku-group-container .popup-sku-row .popup-sku-row__title {
  font-size: 26rpx;
  color: #333;
}

.popup-sku-body .popup-sku-group-container .popup-sku-row .popup-sku-row__item {
  font-size: 24rpx;
  color: #333;
  min-width: 100rpx;
  height: 56rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  border: 2rpx solid #f5f5f5;
  margin: 19rpx 26rpx 0 0;
  padding: 0 16rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.popup-sku-body .popup-sku-group-container .popup-sku-row .popup-sku-row__item.popup-sku-row__item--active {
  border: 2rpx solid var(--themeColor);
  color: var(--themeColor);
  background: #FBFBFB;
}

.popup-sku__stepper-title {
  font-size: 26rpx;
  color: #333;
}

.popup-sku__separately,
.popup-sku__buy {
  width: 264rpx;
  height: 70rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
}

.popup-sku__separately {
  background: linear-gradient(90deg, rgb(75, 145, 230) 0%, rgb(105, 165, 238) 100%);
  color: #fff;
  border-radius: 20rpx;
  width: 340rpx;
}

.popup-sku__buy {
  color: #fff;
  background: linear-gradient(90deg, rgb(19, 115, 231) 0%, rgb(81, 152, 238) 100%);
  border-radius: 20rpx;
  width: 340rpx;
}