/* pages/my/address/edit/editAddress.wxss */
page {
  background-color: #f5f5f5;
}

page .divider-line {
  width: 100%;
  height: 20rpx;
  background-color: #f5f5f5;
}

.form-address .form-content {
  --td-input-vertical-padding: 0;
}

.address-textarea {
  --td-input-disabled-text-color: #333;
  --td-text-color-disabled: #333;
  /* var(--td-input-disabled-text-color, var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))) */
}

.form-cell .t-cell__title {
  width: 144rpx;
  padding-right: 32rpx;
  flex: none !important;
}

.submit_btn {
  box-sizing: border-box;
  padding: 64rpx 30rpx 88rpx 30rpx;
}

.my-form-cell {
  background-color: #fff;
  padding: 20rpx 30rpx 0;
}

.my-form-cell-label {
  font-size: 26rpx;
  color: #555;
}

.my-form-cell-require {
  color: #ff0000;
}

.my-form-cell-content {
  border-bottom: 1rpx solid #efefef;
  padding: 30rpx 0;
}