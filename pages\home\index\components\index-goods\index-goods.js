// pages/home/<USER>/index-goods/index-goods.js
Component({
  properties: {
    goodsList: { type: Array, value: [] },
  },
  data: {

  },
  methods: {
    toAddGoods(e) {
      const { goods } = e.currentTarget.dataset;
      this.triggerEvent('toAddGoods', { goods: goods });
    },
    toGoodsDetail(e) {
      const { goods } = e.currentTarget.dataset;
      wx.navigateTo({ url: '/pages/goodsDetail/goodsDetail?goodsId=' + goods.id });
    },
  }
})