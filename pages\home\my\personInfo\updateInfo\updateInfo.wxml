<!--pages/home/<USER>/personInfo/updateInfo/updateInfo.wxml-->

<view style="display: flex; flex-direction: column;align-items: center;">
  <view style="margin-top: 100rpx;">
    <button style="width: 112rpx;height: 112rpx;border: none;" class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
      <image class="avatar" src="{{userInfo.avatar||defaultAvatarUrl}}"></image>
    </button>
  </view>
  <view style="width: 100%;margin-top: 80rpx;">
    <t-input label="昵称" type="nickname" value="{{userInfo.nickName}}" placeholder="请输入昵称" bind:change="onInputValue" clearable />
  </view>

  <view class="submit_btn">
    <t-button shape="round" block disabled="{{!submitActive}}" bind:tap="doUpdateInfo">保 存</t-button>
  </view>
</view>