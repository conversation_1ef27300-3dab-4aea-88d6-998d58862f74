<wxs module="my">
  var getNum = function (cartList, goods) {
    for (var i = 0; i < cartList.length; ++i) {
      if (cartList[i].goodsId == goods.id) {
        return cartList[i].goodsNum;
      }
    }
    return 0;
  }
  module.exports.getNum = getNum;
</wxs>

<scroll-view class="content" scroll-y scroll-with-animation scroll-top="{{scrollTop}}" bind:scroll="onScroll">
  <view wx:for="{{goodsList}}" wx:key="id" wx:for-item="item">
    <view class="section">
      <view class="title">{{ item.name }} ({{ item.goodsList.length }})</view>
    </view>

    <view class="goods-list">
      <goods-item class="goods-item" wx:for="{{item.goodsList}}" wx:key="id" wx:for-item="goodsItem" goodsDetail="{{goodsItem}}" num="{{ my.getNum(cartList,goodsItem) }}"></goods-item>
      <!-- <goods-item class="goods-item"></goods-item> -->
    </view>
  </view>
</scroll-view>