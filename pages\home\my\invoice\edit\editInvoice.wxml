<!--pages/my/address/edit/editAddress.wxml-->
<view>

  <view class="form-address">
    <form class="form-content">
      <view class="my-form-cell">
        <view class="my-form-cell-label">抬头类型<text class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-radio-group default-value="企业" borderless value="{{form.titleType}}" bind:change="onTitleTypeChange">
            <t-radio block="{{false}}" label="个人" value="个人" />
            <t-radio style="margin-left: 40rpx;" block="{{false}}" label="企业" value="企业" />
            <t-radio style="margin-left: 40rpx;" block="{{false}}" label="组织" value="组织" />
          </t-radio-group>
        </view>
      </view>

      <view class="my-form-cell">
        <view class="my-form-cell-label">发票类型<text class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-radio-group default-value="企业" borderless value="{{form.invoiceType}}" bind:change="onInvoiceTypeChange">
            <t-radio block="{{false}}" label="增值税普通发票（普票）" value="{{1}}" />
            <t-radio wx:if="{{form.titleType==='企业'}}" style="margin-top: 20rpx;" block="{{false}}" label="增值税专用发票（专票）" value="{{2}}" />
          </t-radio-group>
        </view>
      </view>

      <view class="my-form-cell">
        <view class="my-form-cell-label">发票抬头<text class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-input data-item="titleName" borderless maxlength="100" type="text" value="{{form.titleName}}" placeholder="请输入发票抬头" bind:change="onInputValue" />
        </view>
      </view>
      <view wx:if="{{form.titleType==='企业' || form.titleType==='组织'}}" class="my-form-cell">
        <view class="my-form-cell-label">纳税人识别号<text wx:if="{{form.titleType==='企业'}}" class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-input data-item="taxId" borderless maxlength="50" type="text" value="{{form.taxId}}" placeholder="请输入纳税人识别号" bind:change="onInputValue" />
        </view>
      </view>
      <view wx:if="{{form.titleType==='企业'}}" class="my-form-cell">
        <view class="my-form-cell-label">企业注册地址<text wx:if="{{form.invoiceType===2}}" class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-input data-item="registeredAddress" borderless maxlength="200" type="text" value="{{form.registeredAddress}}" placeholder="请输入企业注册地址" bind:change="onInputValue" />
        </view>
      </view>
      <view wx:if="{{form.titleType==='企业'}}" class="my-form-cell">
        <view class="my-form-cell-label">企业注册电话<text wx:if="{{form.invoiceType===2}}" class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-input data-item="registeredPhone" borderless maxlength="50" type="text" value="{{form.registeredPhone}}" placeholder="请输入企业注册电话" bind:change="onInputValue" />
        </view>
      </view>
      <view wx:if="{{form.titleType==='企业'}}" class="my-form-cell">
        <view class="my-form-cell-label">基本开户银行<text wx:if="{{form.invoiceType===2}}" class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-input data-item="bankName" borderless maxlength="50" type="text" value="{{form.bankName}}" placeholder="请输入基本开户银行" bind:change="onInputValue" />
        </view>
      </view>
      <view wx:if="{{form.titleType==='企业'}}" class="my-form-cell">
        <view class="my-form-cell-label">基本开户账号<text wx:if="{{form.invoiceType===2}}" class="my-form-cell-require">*</text></view>
        <view class="my-form-cell-content">
          <t-input data-item="bankAccount" borderless maxlength="50" type="text" value="{{form.bankAccount}}" placeholder="请输入基本开户账号" bind:change="onInputValue" />
        </view>
      </view>
      <view class="my-form-cell">
        <view class="my-form-cell-label">接收邮箱</view>
        <view class="my-form-cell-content">
          <t-input data-item="receiveEmail" borderless maxlength="50" type="text" value="{{form.receiveEmail}}" placeholder="请输入接收邮箱" bind:change="onInputValue" />
        </view>
      </view>

      <t-cell-group>
        <view class="divider-line" />
        <t-cell title="设置为默认" bordered="{{false}}">
          <t-switch slot="note" value="{{form.isDefault}}" custom-value="{{[1,0]}}" bind:change="onCheckDefaultAddress" />
        </t-cell>
      </t-cell-group>

      <view class="submit_btn">
        <t-button shape="round" block disabled="{{!submitActive}}" bind:tap="formSubmit">保 存</t-button>
      </view>
    </form>
  </view>

</view>