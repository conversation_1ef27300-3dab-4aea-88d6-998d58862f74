// pages/home/<USER>
import * as GoodsApi from "~/api/GoodsApi.js"

Page({
  data: {
    selectTypeId: 0,
    typeList: [],
    goodsList: [],
    showGoods: {},
    isSpuSelectPopupShow: false,
    goodsInfo: {},
    tempId: ""
  },
  doBuyNow(e) {
    this.handlePopupHide(() => {
      const params = e.detail;
      wx.navigateTo({ url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}` });
    });
  },
  doAddCart(params) {
    this.handlePopupHide();
  },
  toAddGoods(e) {
    const { goods } = e.currentTarget.dataset;
    this.setData({ isSpuSelectPopupShow: true, goodsInfo: goods });
    const goodsSpecsPopup = this.selectComponent('#goodsSpecsPopup');
    goodsSpecsPopup.init();
  },
  handlePopupHide(func) {
    this.setData({ isSpuSelectPopupShow: false, }, func);
  },
  toGoodsDetail(e) {
    const { goods } = e.currentTarget.dataset;
    wx.navigateTo({ url: '/pages/goodsDetail/goodsDetail?goodsId=' + goods.id });
  },
  onTypeChange(e) {
    const { value } = e.detail;
    console.log('---', value);
    this.setData({ selectTypeId: value });
    this.setData({
      showGoods: this.data.goodsList.find(item => item.id === this.data.selectTypeId),
    });
  },
  getClassifyData() {
    wx.showLoading({ title: '正在加载...', mask: true });
    const selectedBuildingId = wx.getStorageSync('selectedBuildingId');
    GoodsApi.classifyData(1, selectedBuildingId).then(res => {
      this.setData({ typeList: res.typeList, goodsList: res.goods });
      if (this.data.tempId) {
        this.setData({ selectTypeId: this.data.tempId, tempId: "" });
      } else {
        this.setData({ selectTypeId: this.data.typeList[0].id });
      }
      this.setData({
        showGoods: this.data.goodsList.find(item => item.id === this.data.selectTypeId),
      });
      if (this.data.typeList.length > 0) {
        this.setData({ currentType: this.data.typeList[0] });
      }
      wx.hideLoading();
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
    });
  },
  onLoad(options) {
    this.getClassifyData();

    // 设置分享菜单
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  onReady() {
  },
  onShow() {
    this.getTabBar().init();
    try {
      const tempId = wx.getStorageSync('tempId')
      if (tempId) {
        this.setData({ tempId: tempId });
        wx.removeStorage({ key: 'tempId' })
        if (this.data.goodsList.length > 0) {
          this.setData({ selectTypeId: this.data.tempId, tempId: "" });
          this.setData({
            showGoods: this.data.goodsList.find(item => item.id === this.data.selectTypeId),
          });
        }
      }
    } catch (error) {
    }

  },
  onHide() {
  },
  onUnload() {
  },

  // 分享给好友
  onShareAppMessage() {
    return {
      title: '楼宇企业全生命周期生态服务 - 服务',
      path: '/pages/home/<USER>/service',
      imageUrl: 'https://hqt.csylch.com/platform/hqt.jpg'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '楼宇企业全生命周期生态服务 - 服务',
      imageUrl: 'https://hqt.csylch.com/platform/hqt.jpg'
    };
  }
})