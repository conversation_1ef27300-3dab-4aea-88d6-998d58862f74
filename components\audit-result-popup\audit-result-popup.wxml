<!--components/audit-result-popup/audit-result-popup.wxml-->
<t-popup visible="{{visible}}" placement="center" bind:visible-change="onVisibleChange">
  <view class="audit-popup">
    <!-- 头部 -->
    <view class="popup-header">
      <view class="header-icon {{auditResult === 'agree' ? 'success-icon' : 'fail-icon'}}">
        <t-icon name="{{auditResult === 'agree' ? 'check-circle-filled' : 'close-circle-filled'}}" size="60rpx" />
      </view>
      <view class="header-title">{{auditResult === 'agree' ? '审核通过' : '审核不通过'}}</view>
    </view>

    <!-- 内容区域 -->
    <view class="popup-content">
      <!-- 审核通过 -->
      <view wx:if="{{auditResult === 'agree'}}" class="success-content">
        <view class="content-text">您的退款申请已通过审核</view>
        <view class="content-desc">请耐心等待，退款将在1-3个工作日内到账</view>
      </view>

      <!-- 审核不通过 -->
      <view wx:else class="fail-content">
        <view class="content-text">很抱歉，您的退款申请未通过审核</view>
        <view class="reason-section">
          <view class="reason-title">不通过原因：</view>
          <view class="reason-text">{{auditRemark || '暂无详细说明'}}</view>
        </view>
        <view class="content-desc">如有疑问，请联系客服咨询</view>
      </view>

      <!-- 审核时间 -->
      <view wx:if="{{auditTime}}" class="audit-time">
        <view class="time-label">审核时间：</view>
        <view class="time-value">{{auditTime}}</view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-footer">
      <view class="confirm-btn" bind:tap="onConfirm">知道了</view>
    </view>
  </view>
</t-popup>
