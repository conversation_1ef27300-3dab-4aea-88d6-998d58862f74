<view class="custom-sidebar">
  <scroll-view class="c-sidebar" scroll-y>

    <view class="c-sidebar-layer">
      <view class="c-sidebar-item {{currentType.id==item.id?'selected':''}}" wx:for="{{ typeList }}" wx:for-item="item" wx:for-index="index" wx:key="index" wx:key="index" data-item="{{item}}" bind:tap="onClick">
        <view class="c-sidebar-icon">
          <image class="c-sidebar-icon-image" src="{{item.pic}}"></image>
        </view>
        <view class="c-sidebar-item-txt">{{item.name}}</view>
      </view>

    </view>
  </scroll-view>
</view>