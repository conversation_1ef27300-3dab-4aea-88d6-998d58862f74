// components/goods-specs-popup/goods-specs-popup.js
import { checkLogin } from '~/api/AuthApi';
import * as ShopCarApi from "~/api/ShopCarApi.js"

Component({
  properties: {
    show: { type: Boolean, value: false },
    goodsInfo: { type: Object, value: {} }
  },
  data: {
    imgSrc: "",
    currentPrice: "",
    specList: [],
    buyNum: 1
  },
  methods: {
    getChooseItemId() {
      if (this.data.goodsInfo.specType === 1) {
        return this.data.goodsInfo.goodsItemList[0].id;
      } else if (this.data.goodsInfo.specType === 2) {
        const specItemIds = this.getAllSelectedIds();
        const goodsItem = this.data.goodsInfo.goodsItemList.find((item) => item.specItemIds == specItemIds);
        if (goodsItem) {
          return goodsItem.id;
        }
      }
      return "";
    },
    doAddCart(params) {
      wx.showLoading({ title: '正在提交...', mask: true });
      ShopCarApi.shopCarAdd(params.goodsId, params.goodsItemId, params.buyNum).then(res => {
        wx.showToast({ title: '添加成功', icon: 'success', duration: 2000 });
        wx.hideLoading();
        this.triggerEvent('doAddCart', { buyNum: this.data.buyNum, goodsId: this.data.goodsInfo.id, goodsItemId: this.getChooseItemId() });
      }).catch(error => {
        console.log(error);
        wx.hideLoading();
      });
    },
    toAddCart() {
      const goodsItemId = this.getChooseItemId();
      if (goodsItemId) {
        checkLogin().then(res => {
          this.doAddCart({ buyNum: Number(this.data.buyNum), goodsId: this.data.goodsInfo.id, goodsItemId: goodsItemId })
        }).catch((e) => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        });
      }
    },
    toBuyNow() {
      const goodsItemId = this.getChooseItemId();
      if (goodsItemId) {
        checkLogin().then(res => {
          this.triggerEvent('doBuyNow', { buyNum: this.data.buyNum, goodsId: this.data.goodsInfo.id, goodsItemId: goodsItemId });
        }).catch((e) => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        });
      }
    },
    toChooseItem(e) {
      if (this.data.goodsInfo.specType === 1) {
        return;
      } else if (this.data.goodsInfo.specType === 2) {
        const { specIndex, specItemIndex } = e.currentTarget.dataset;
        this.data.specList[specIndex].specItemList.forEach((item, index) => {
          item.isSelected = index === specItemIndex;
        });
        this.setData({ specList: this.data.specList });
        this.calcPrice();
      }
    },
    calcPrice() {
      const specItemIds = this.getAllSelectedIds();
      if (specItemIds) {
        const goodsItem = this.data.goodsInfo.goodsItemList.find((item) => item.specItemIds == specItemIds);
        if (goodsItem) {
          this.setData({ currentPrice: goodsItem.price });
        }
      }
    },
    // 判断是否所有的sku都已经选中,全选时返回ids
    getAllSelectedIds() {
      const totalNum = this.data.specList.length;
      let selectedNum = 0, specItemIds = [];
      this.data.specList.forEach((item) => {
        item.specItemList.forEach((specItem) => {
          if (specItem.isSelected) {
            selectedNum++;
            specItemIds.push(specItem.specItemId);
          }
        });
      });
      if (totalNum === selectedNum) {
        return specItemIds.join(",");
      }
      return "";
    },
    handleBuyNumChange(e) {
      const { value } = e.detail;
      this.setData({ buyNum: value });
    },
    init() {
      this.setData({ currentPrice: "", imgSrc: this.data.goodsInfo.goodsItemList[0].pic.split(",")[0], buyNum: 1 });
      if (this.data.goodsInfo.specType === 1) {
        const goodsItem = this.data.goodsInfo.goodsItemList[0];
        this.setData({ currentPrice: goodsItem.price });
        this.setData({
          specList: [{ specId: 1, specName: "规格", specItemList: [{ specItemId: 2, specItemName: "默认", isSelected: true }] }]
        });

      } else if (this.data.goodsInfo.specType === 2) {
        const specInfo = JSON.parse(this.data.goodsInfo.specInfo);
        specInfo.forEach(item => {
          item.specItemList[0].isSelected = true;
        });
        this.setData({ specList: specInfo });
        this.calcPrice();
      }

    },
    handlePopupHide() {
      this.triggerEvent('closeSpecsPopup', { show: false });
    }
  }
})