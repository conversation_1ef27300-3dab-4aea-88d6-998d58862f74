<!--pages/home/<USER>
<!-- 自定义导航栏 -->
<custom-navbar title="耀楼惠企通" show-back="{{false}}" />

<wxs module="my">
  var mainPic = function (pic) {
    return pic.split(",")[0];
  }

  var getPrice = function (goods) {
    if (goods.specType === 1) {
      return goods.goodsItemList[0].price;
    } else if (goods.specType === 2) {
      var price = 0;
      for (var i = 0; i < goods.goodsItemList.length; i++) {
        if (price == 0 || price > goods.goodsItemList[i].price) {
          price = goods.goodsItemList[i].price;
        }
      }
      return price;
    }
    return 0;
  }
  module.exports.mainPic = mainPic;
  module.exports.getPrice = getPrice;
</wxs>

<view class="side-bar-wrapper">
  <t-side-bar style="width: 230rpx;" value="{{selectTypeId}}" default-value="{{selectTypeId}}" bind:change="onTypeChange">
    <t-side-bar-item wx:for="{{typeList}}" wx:key="index" value="{{item.id}}" label="{{item.name}}" />
  </t-side-bar>

  <view class="content" style="transform: translateY(-{{typeIndex * 100}}%)">
    <scroll-view class="section" scroll-y scroll-with-animation show-scrollbar="{{false}}">
      <view class="title">{{showGoods.name}}</view>

      <block wx:if="{{showGoods.goodsList.length>0}}">
        <view class="flex-row item-leyout" wx:for="{{showGoods.goodsList}}" wx:key="index" wx:for-item="goods" data-goods="{{goods}}" bind:tap="toGoodsDetail">
          <t-image shape="round" src="{{ my.mainPic(goods.pic) }}" slot="image" lazy t-class="image" />
          <view class="flex-column item-right" style="flex: 1;">
            <view class="item-name lines-2">{{goods.name}}</view>
            <view class="flex-row-center" style="justify-content: space-between;">
              <view>
                <text class="item-price1">￥</text>
                <text class="item-price2">{{my.getPrice(goods)}}</text>
              </view>
              <image class="goods-item-add" src="/static/images/addicon.png" mode="aspectFit" data-goods="{{goods}}" catch:tap="toAddGoods" />
            </view>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="empty-state">
          <view class="empty-icon">📦</view>
          <view class="empty-text">该分类下暂无相关内容哦</view>
        </view>
      </block>
    </scroll-view>
  </view>


</view>

<goods-specs-popup id="goodsSpecsPopup" bind:doBuyNow="doBuyNow" bind:doAddCart="doAddCart" goodsInfo="{{goodsInfo}}" show="{{isSpuSelectPopupShow}}" bind:closeSpecsPopup="handlePopupHide">
</goods-specs-popup>

<!-- 悬浮客服按钮 -->
<floating-kefu init-x="600" init-y="700" />