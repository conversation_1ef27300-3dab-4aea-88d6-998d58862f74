// pages/order/orderDetail/orderDetail.js
import * as OrderApi from "~/api/OrderApi.js"
import { getInvoicePromise } from '~/pages/home/<USER>/invoice/util';

Page({
  data: {
    orderDetail: {},
    address: {},
    goodsList: [],
    showPaymentActionSheet: false,
    showOfflinePaymentOverlay: false,
    paymentActions: [
      {
        name: '微信支付',
        color: '#07c160'
      },
      {
        name: '线下支付',
        color: '#1989fa'
      }
    ]
  },
  doTakeInvoice() {
    getInvoicePromise().then((invoice) => {
      setTimeout(() => {
        this.showInvoiceConfirm(invoice, this.data.orderDetail.orderCode);
      }, 500);
    }).catch(() => { });
    wx.navigateTo({ url: '/pages/home/<USER>/invoice/invoice?mode=select' })
  },
  doSaveInvoice() {
    const orderDetail = this.data.orderDetail;

    // 检查发票信息是否存在
    if (!orderDetail.orderInvoiceList || orderDetail.orderInvoiceList.length === 0) {
      wx.showToast({
        title: '发票信息不存在',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    const invoice = orderDetail.orderInvoiceList[0];
    const filePath = invoice.filePath;

    // 检查文件路径是否存在
    if (!filePath) {
      wx.showToast({
        title: '发票文件不存在',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    console.log('开始下载发票:', filePath);

    // 检查文件类型
    const fileExtension = this.getFileExtension(filePath);
    console.log('文件类型:', fileExtension);

    const deviceInfo = wx.getDeviceInfo();

    if (deviceInfo.platform === 'windows') {
      // PC端直接保存到磁盘
      console.log('PC端下载:', deviceInfo.platform);
      wx.saveFileToDisk({
        filePath: filePath,
        success(res) {
          console.log('PC端保存成功:', res);
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000
          });
        },
        fail(res) {
          console.error('PC端保存失败:', res);
          wx.showToast({
            title: '保存失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    } else {
      // 移动端下载并预览
      console.log('移动端下载:', deviceInfo.platform);

      // 检查文件类型是否支持预览
      if (!this.isSupportedFileType(fileExtension)) {
        wx.showModal({
          title: '文件类型提示',
          content: `该文件类型(.${fileExtension})可能无法预览，是否继续下载？`,
          confirmText: '继续',
          cancelText: '取消',
          success: (modalRes) => {
            if (modalRes.confirm) {
              this.downloadAndPreviewFile(filePath);
            }
          }
        });
        return;
      }

      this.downloadAndPreviewFile(filePath);
    }
  },

  // 下载并预览文件
  downloadAndPreviewFile(filePath) {
    wx.showLoading({
      title: '正在下载...',
      mask: true
    });

    wx.downloadFile({
      url: filePath,
      success: (res) => {
        console.log('下载成功:', res);
        wx.hideLoading();

        // 检查下载是否成功
        if (res.statusCode === 200) {
          console.log('开始预览文档:', res.tempFilePath);

          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: function (openRes) {
              console.log('打开文档成功:', openRes);
              wx.showToast({
                title: '打开成功',
                icon: 'success',
                duration: 1500
              });
            },
            fail: function (openErr) {
              console.error('打开文档失败:', openErr);

              // 分析失败原因
              let failReason = '预览失败';
              if (openErr.errMsg) {
                if (openErr.errMsg.includes('not support')) {
                  failReason = '不支持该文件格式';
                } else if (openErr.errMsg.includes('file not exist')) {
                  failReason = '文件不存在';
                } else if (openErr.errMsg.includes('permission')) {
                  failReason = '没有权限访问文件';
                }
              }

              // 如果预览失败，提供保存选项
              wx.showModal({
                title: failReason,
                content: '无法预览该文件，是否保存到手机？',
                confirmText: '保存',
                cancelText: '取消',
                success(modalRes) {
                  if (modalRes.confirm) {
                    // 尝试保存文件
                    wx.saveFile({
                      tempFilePath: res.tempFilePath,
                      success(saveRes) {
                        console.log('保存文件成功:', saveRes);
                        wx.showToast({
                          title: '保存成功',
                          icon: 'success',
                          duration: 2000
                        });
                      },
                      fail(saveErr) {
                        console.error('保存文件失败:', saveErr);
                        wx.showToast({
                          title: '保存失败，请检查存储权限',
                          icon: 'none',
                          duration: 3000
                        });
                      }
                    });
                  }
                }
              });
            }
          });
        } else {
          console.error('下载失败，状态码:', res.statusCode);
          wx.showToast({
            title: `下载失败(${res.statusCode})`,
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('下载失败:', err);
        wx.hideLoading();

        let errorMsg = '下载失败';
        if (err.errMsg) {
          if (err.errMsg.includes('network')) {
            errorMsg = '网络连接失败，请检查网络';
          } else if (err.errMsg.includes('timeout')) {
            errorMsg = '下载超时，请重试';
          } else if (err.errMsg.includes('url')) {
            errorMsg = '文件地址无效';
          } else if (err.errMsg.includes('permission')) {
            errorMsg = '没有下载权限';
          }
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },
  showInvoiceConfirm(invoice, orderCode) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: `确认为"${invoice.titleName}"开票？`,
      success(res) {
        if (res.confirm) {
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.addInvoice({ id: invoice.id, orderCode: orderCode }).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.init(orderCode);
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  // 付款 - 显示支付方式选择
  doPay(e) {
    console.log('点击付款按钮');
    this.setData({
      showPaymentActionSheet: true
    });
  },

  // 选择支付方式
  onSelectPaymentMethod(event) {
    console.log('选择支付方式事件:', event.detail);
    const selectedItem = event.detail;

    this.setData({
      showPaymentActionSheet: false
    });

    if (selectedItem.name === '微信支付') {
      // 微信支付
      console.log('选择了微信支付');
      this.doWechatPay();
    } else if (selectedItem.name === '线下支付') {
      // 线下支付
      console.log('选择了线下支付');
      this.showOfflinePaymentInfo();
    }
  },

  // 关闭支付方式选择面板
  onClosePaymentActionSheet() {
    this.setData({
      showPaymentActionSheet: false
    });
  },

  // 微信支付逻辑（原有逻辑）
  doWechatPay() {
    console.log('开始微信支付流程');
    const orderCode = this.data.orderDetail.orderCode;
    wx.showLoading({ title: '正在请求...', mask: true });
    OrderApi.getWxPay({ orderCode }).then(res => {
      console.log('微信支付API响应:', res);
      wx.hideLoading();
      wx.requestPayment({
        timeStamp: res.timeStamp,
        nonceStr: res.nonceStr,
        package: res.packageValue,
        signType: res.signType,
        paySign: res.paySign,
        success: (res) => {
          console.log('微信支付成功:', res);
          wx.showToast({ title: '支付成功', icon: 'success', duration: 2000 });
          this.init(this.data.orderDetail.orderCode);
        },
        fail: (res) => {
          console.log('微信支付失败:', res);
        }
      });
    }).catch(error => {
      console.log('微信支付API错误:', error);
      wx.hideLoading();
    });
  },

  // 显示线下支付信息
  showOfflinePaymentInfo() {
    console.log('显示线下支付信息');
    this.setData({
      showOfflinePaymentOverlay: true
    });
  },

  // 关闭线下支付信息
  closeOfflinePaymentInfo() {
    this.setData({
      showOfflinePaymentOverlay: false
    });
  },

  // 一键复制支付信息
  copyPaymentInfo() {
    const paymentInfo = `账户名称：长沙耀楼产业互联科技服务有限公司
账户号码：731912140110006
开户银行：招商银行股份有限公司长沙晓园支行
纳税识别号：91430104MAEBFJ0G7C
公司地址：湖南省长沙市岳麓区岳麓街道溁左路中南大学科技园研发总部 6 栋 322 房`;

    wx.setClipboardData({
      data: paymentInfo,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击事件冒泡到遮罩层
  },

  // 获取文件扩展名
  getFileExtension(filePath) {
    if (!filePath) return '';
    const lastDotIndex = filePath.lastIndexOf('.');
    if (lastDotIndex === -1) return '';
    return filePath.substring(lastDotIndex + 1).toLowerCase();
  },

  // 检查文件类型是否支持预览
  isSupportedFileType(extension) {
    const supportedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
    return supportedTypes.includes(extension.toLowerCase());
  },

  // 测试动作面板
  testActionSheet() {
    console.log('测试按钮点击');
    console.log('当前支付方式数据:', this.data.paymentActions);
    this.setData({
      showPaymentActionSheet: true
    });
  },
  // 取消订单
  doCancelOrder(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认取消订单？',
      success(res) {
        if (res.confirm) {
          const { item } = e.currentTarget.dataset;
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.orderCancel(item.orderCode).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.init(item.orderCode);
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  // 确认收货
  doTakeGoods(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认完成订单？',
      success(res) {
        if (res.confirm) {
          const { item } = e.currentTarget.dataset;
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.orderTakeGoods(item.orderCode).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });

            // 收货成功后跳转到评价页面
            setTimeout(() => {
              _that.navigateToComment(item);
            }, 2000);

          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },

  // 跳转到商品详情页面
  navigateToGoodsDetail(e) {
    const { item } = e.currentTarget.dataset;
    console.log('点击商品项:', item); // 调试输出

    if (item && item.goodsId) {
      wx.navigateTo({
        url: `/pages/goodsDetail/goodsDetail?goodsId=${item.goodsId}`
      });
    } else {
      console.log('商品ID不存在，无法跳转');
      wx.showToast({
        title: '商品信息异常',
        icon: 'none'
      });
    }
  },

  // 跳转到评价页面
  navigateToComment(orderItem) {
    const { goodsList } = this.data;
    if (goodsList && goodsList.length > 0) {
      // 取第一个商品信息作为评价对象
      const goodsInfo = goodsList[0];
      const goodsInfoStr = encodeURIComponent(JSON.stringify(goodsInfo));
      wx.navigateTo({
        url: `/pages/comment/add/add?orderCode=${orderItem.orderCode}&goodsInfo=${goodsInfoStr}`
      });
    }
  },
  async init(orderCode) {
    try {
      const result = await OrderApi.orderDetail(orderCode);
      console.log('订单详情数据:', result); // 调试输出
      console.log('商品列表数据:', result.orderItemList); // 调试输出
      this.setData({ orderDetail: result, address: result.orderDelivery, goodsList: result.orderItemList });
    } catch (error) {
      console.error('err:', error);
    }
  },
  onLoad(options) {
    console.log('订单详情页面加载，参数:', options);
    console.log('支付方式数据:', this.data.paymentActions);
    this.init(options.orderCode);
  },
  onReady() {

  },
  onShow() {

  },
  onHide() {

  },
  onUnload() {

  }
})