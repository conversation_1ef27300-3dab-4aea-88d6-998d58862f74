const AuthStepType = {
  ONE: 1,
  TWO: 2,
  THREE: 3,
};

Component({
  options: {
    multipleSlots: true,
  },
  properties: {
    currAuthStep: {
      type: Number,
      value: AuthStepType.ONE,
    },
    userInfo: {
      type: Object,
      value: {},
    },
    isNeedGetUserInfo: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    defaultAvatarUrl: '/static/images/avatar.png',
    AuthStepType,
  },
  methods: {
    gotoAuth() {
      wx.navigateTo({
        url: '/pages/auth/auth',
      });
    },
    gotoUserEditPage() {
      this.triggerEvent('gotoUserEditPage');
    },
  },
});
