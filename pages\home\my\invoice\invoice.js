// pages/my/invoice.js
import InvoiceApi from '~/api/InvoiceApi';
import { resolveInvoice, rejectInvoice } from './util';

Page({
  data: {
    selectMode: false,
    invoiceList: []
  },
  async init() {
    try {
      const result = await InvoiceApi.invoiceList();
      this.setData({ invoiceList: result.records });
    } catch (error) {
      console.error('err:', error);
    }
  },
  gotoAdd() {
    wx.navigateTo({ url: '/pages/home/<USER>/invoice/edit/editInvoice' })
  },
  gotoEdit(e) {
    const item = e.currentTarget.dataset.item;
    wx.navigateTo({ url: '/pages/home/<USER>/invoice/edit/editInvoice?id=' + item.id })
  },
  onDelete(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认删除吗？',
      success(res) {
        if (res.confirm) {
          const item = e.currentTarget.dataset.item;
          wx.showLoading({ title: '正在提交...', mask: true });
          InvoiceApi.invoiceDelete(item.id).then(res => {
            _that.init(true);
            wx.hideLoading();
          }).catch(error => {
            wx.hideLoading();
          });
        } else if (res.cancel) {
        }
      }
    });
  },
  doSelect(e) {
    if (this.data.selectMode) {
      const { item } = e.currentTarget.dataset;
      resolveInvoice(item);
      wx.navigateBack({ delta: 1 });
    }
  },
  onLoad(options) {
    this.setData({ selectMode: "select" == options.mode });
  },
  onShow() {
    this.init(true);
  }
})