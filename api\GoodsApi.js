
import { request } from "~/utils/http.js"

function classifyData(serviceGoods, bcId) {
  return request('/app/goods/classifyData', { serviceGoods, bcId });
}

function goodsList(params) {
  return request('/app/goods/list', { current: 1, size: -1, ...params });
}

function searchHotGoods(params) {
  return request('/app/goods/searchHotGoods', { current: 1, size: -1, ...params });
}

function goodsInfo(params) {
  return request('/app/goods/info', { ...params });
}

module.exports = {
  goodsList, classifyData, goodsInfo, searchHotGoods
}