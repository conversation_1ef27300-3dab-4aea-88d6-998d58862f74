// pages/auth/auth.js
import { loginByWeixin, hasRegister } from "~/api/AuthApi.js"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isAgree: false,
    logoUrl: __wxConfig.accountInfo.icon,
    name: "耀楼智企通",
    hasRegister: true
  },
  wxLogin() {
    this.doLogin();
  },
  wxPhoneLogin(p) {
    if (p.detail.errMsg == "getPhoneNumber:ok") {
      this.doLogin(p);
    }
  },
  doLogin(p) {
    if (this.data.isAgree) {
      wx.showLoading({ title: '正在请求...', mask: true });
      loginByWeixin(p && p.detail).then(res => {
        console.log(res);
        wx.hideLoading();
        wx.navigateBack();
      }).catch(error => {
        if (error.status == 1) {
          wx.showToast({ title: "请使用手机号码登录", icon: 'none', duration: 3000 });
          this.setData({ hasRegister: false });
        }
        console.log(error);
        wx.hideLoading();
      });
    }
  },
  back() {
    wx.navigateBack();
  },
  onChangeRadio(event) {
    const { checked } = event.detail;
    this.setData({ isAgree: checked });
  },
  toUserWeb() {
    wx.navigateTo({ url: '/pages/webpage/webpage?url=https://hqt.csylch.com/resource/user.html' });
  },
  toYsWeb() {
    wx.navigateTo({ url: '/pages/webpage/webpage?url=https://hqt.csylch.com/resource/ys.html' });
  },
  onLoad(options) {
    wx.showLoading({ title: '正在请求...', mask: true });
    hasRegister().then(res => {
      this.setData({ hasRegister: res });
      wx.hideLoading();
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
    });
  },
})