// components/comment-detail-popup/comment-detail-popup.js
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    comment: {
      type: Object,
      value: {}
    },
    orderInfo: {
      type: Object,
      value: {}
    }
  },

  methods: {
    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 获取评分文字描述
    getRatingText(rating) {
      switch (rating) {
        case 1: return '很差';
        case 2: return '较差';
        case 3: return '一般';
        case 4: return '满意';
        case 5: return '非常满意';
        default: return '未知';
      }
    },

    // 阻止事件冒泡
    preventBubble() {
      // 阻止点击内容区域时关闭弹窗
    }
  }
});
