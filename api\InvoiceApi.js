
import { request } from "~/utils/http.js"

function invoiceList() {
  return request('/app/invoice/list', { current: 1, size: -1 });
}
function invoiceAdd(params) {
  return request('/app/invoice/add', { ...params });
}
function invoiceUpdate(params) {
  return request('/app/invoice/update', { ...params });
}
function invoiceInfo(id) {
  return request('/app/invoice/info', { id });
}
function invoiceDelete(id) {
  return request('/app/invoice/delete', { id });
}

module.exports = {
  invoiceList, invoiceAdd, invoiceUpdate, invoiceInfo, invoiceDelete
}