<!--pages/order/refund/refund.wxml-->
<wxs module="utils">
  var mainPic = function (pic) {
    if (!pic) return '';
    return pic.split(",")[0];
  }
  module.exports = { mainPic: mainPic };
</wxs>

<view class="refund-container">
  <!-- 订单信息 -->
  <view class="order-info-section">
    <view class="order-number">订单号：{{orderCode}}</view>

    <!-- 商品信息 -->
    <view class="goods-info" wx:if="{{goodsInfo}}">
      <image class="goods-image" src="{{utils.mainPic(goodsInfo.pic)}}" mode="aspectFill" />
      <view class="goods-details">
        <view class="goods-name">{{goodsInfo.goodsName}}</view>
        <view class="goods-spec">规格：{{goodsInfo.specDesc || '默认'}}</view>
        <view class="goods-price">￥{{goodsInfo.price}}</view>
      </view>
    </view>
  </view>

  <!-- 退款理由 -->
  <view class="reason-section">
    <view class="section-title">退款理由</view>
    <van-field
      value="{{applyReason}}"
      placeholder="请输入退款理由"
      type="textarea"
      maxlength="200"
      show-word-limit
      autosize
      bind:change="onReasonChange"
      border="{{false}}"
    />
  </view>

  <!-- 退款数量 -->
  <view class="quantity-section">
    <view class="section-title">退款数量</view>
    <view class="quantity-row">
      <van-stepper
        value="{{applyQuantity}}"
        min="1"
        max="{{maxQuantity}}"
        bind:change="onQuantityChange"
      />
    </view>
  </view>

  <!-- 退款金额 -->
  <view class="amount-section">
    <view class="amount-row">
      <view class="amount-label">退款总价</view>
      <view class="amount-value">￥{{applyAmount}}</view>
    </view>
  </view>
</view>

<!-- 底部按钮 -->
<view class="bottom-buttons">
  <view class="refund-cancel-btn" bind:tap="onCancel">取消</view>
  <view class="refund-confirm-btn {{submitting ? 'disabled' : ''}}" bind:tap="onConfirm">
    <text wx:if="{{submitting}}">提交中...</text>
    <text wx:else>确定</text>
  </view>
</view>
