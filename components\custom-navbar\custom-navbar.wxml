<view class="custom-navbar" style="height: {{showBuildingSelector ? 76 : 44}}px; padding-top: {{statusBarHeight}}px; background-color: {{backgroundColor}};">
  <view class="navbar-content">
    <!-- 左侧区域 -->
    <view class="navbar-left">
      <!-- 返回按钮 -->
      <view class="back-btn" wx:if="{{showBack}}" bind:tap="onBack">
        <t-icon name="chevron-left" size="40rpx" color="{{textColor}}" />
      </view>
    </view>

    <!-- 中间标题区域 -->
    <view class="navbar-center">
      <view class="navbar-title" style="color: {{textColor}};">{{title}}</view>
    </view>

    <!-- 右侧操作区域 -->
    <view class="navbar-right">
      <slot name="right"></slot>
    </view>
  </view>

  <!-- 楼宇选择器 - 单独一行 -->
  <view class="building-selector-row" wx:if="{{showBuildingSelector}}">
    <view class="building-selector" bind:tap="onBuildingSelectorTap">
      <t-icon name="location" size="28rpx" color="{{textColor}}" class="location-icon" />
      <view class="building-text">
        <view class="building-name-row">
          <text class="building-name" style="color: {{textColor}};">{{buildingName || '选择楼宇'}}</text>
          <t-icon name="chevron-down" size="24rpx" color="{{textColor}}" class="chevron-icon" />
        </view>
        <!-- 距离显示已隐藏 -->
        <!-- <text class="building-distance" style="color: {{textColor}};" wx:if="{{buildingDistance}}">距离: {{buildingDistance}}</text> -->
      </view>
    </view>
  </view>
</view>

<!-- 占位区域，防止内容被导航栏遮挡 -->
<view class="navbar-placeholder" style="height: {{navBarHeight}}px;"></view>
