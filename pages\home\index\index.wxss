/* pages/home/<USER>/
page {
  background-color: var(--pageBgColor);
  height: 100vh;
}

/* 底部安全区域 */
.safe-area-bottom {
  padding-bottom: calc(var(--tabbarHeight) + env(safe-area-inset-bottom));
}

/* 精选推荐标题样式 */
.section-title-container {
  padding: 30rpx 30rpx 0rpx;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  padding: 0 30rpx;
  position: relative;
  z-index: 2;
}

/* 分割线样式 - 圆头渐变缩小 */
.title-divider {
  width: 40px;
  height: 4rpx;
  position: relative;
  background: transparent;
}

.left-divider {
  background: #cccccc;
  border-radius: 0 4rpx 4rpx 0;
  transform-origin: right center;
  background: linear-gradient(to left, #cccccc 0%, #cccccc 30%, transparent 100%);
  clip-path: polygon(0% 25%, 70% 0%, 100% 0%, 100% 100%, 70% 100%, 0% 75%);
}

.right-divider {
  background: #cccccc;
  border-radius: 4rpx 0 0 4rpx;
  transform-origin: left center;
  background: linear-gradient(to right, #cccccc 0%, #cccccc 30%, transparent 100%);
  clip-path: polygon(0% 0%, 30% 0%, 100% 25%, 100% 75%, 30% 100%, 0% 100%);
}

/* 安卓系统顶部透明占位区域 */
.android-top-spacer {
  height: 10px;
  background: #2492F2;
  width: 100%;
}

/* 搜索框容器 */
.search-container {
  padding: 0rpx 30rpx 10rpx;
  position: relative;
  z-index: 3;
  margin-top: 10rpx;
}

/* 搜索框样式 */
.search-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.search-placeholder {
  margin-left: 16rpx;
  color: #999999;
  font-size: 28rpx;
}

/* Swiper容器样式 */
.swiper-container {
  position: relative;
  background: linear-gradient(to bottom, #2492F2 0%, #F6F6F6 300rpx, transparent 300rpx);
}

/* Swiper背景容器 */
.swiper-background {
  margin: 20rpx;
  border-radius: 12rpx;
  position: relative;
}

/* 自定义Swiper样式 */
.custom-swiper {
  margin: 0 !important;
  background: transparent !important;
}

/* 覆盖TDesign swiper的默认背景 */
.custom-swiper .t-swiper {
  background: transparent !important;
}

.custom-swiper .t-swiper__container {
  background: transparent !important;
}



.menu-container {
  margin-top: 10rpx;
  background-color: white;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 20rpx 15rpx;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
  z-index: 2;
}

.menu-container-item {
  width: 180rpx;
  height: 140rpx;
}

.menu-container-img {
  height: 66rpx;
}

.menu-container-txt {
  margin-top: 10rpx;
  color: #333;
}