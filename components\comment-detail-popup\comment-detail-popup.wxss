/* components/comment-detail-popup/comment-detail-popup.wxss */
.comment-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.comment-popup-overlay.show {
  opacity: 1;
  visibility: visible;
}

.comment-popup-content {
  background-color: #fff;
  border-radius: 20rpx;
  width: 85%;
  max-width: 550rpx;
  max-height: 75vh;
  overflow-y: auto;
  transform: scale(0.8);
  transition: transform 0.3s ease;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.comment-popup-overlay.show .comment-popup-content {
  transform: scale(1);
}

/* 头部 */
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.close-icon {
  font-size: 36rpx;
  color: #999;
  line-height: 1;
}

/* 商品信息 */
.goods-info {
  display: flex;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.goods-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.goods-spec {
  font-size: 22rpx;
  color: #999;
}

/* 评价内容 */
.comment-content {
  padding: 30rpx;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.rating-text {
  font-size: 28rpx;
  color: #2492F2;
  font-weight: 500;
}

.content-section {
  margin-bottom: 30rpx;
}

.content-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  background-color: #f8f8f8;
  padding: 24rpx;
  border-radius: 12rpx;
  min-height: 120rpx;
}

.time-section {
  text-align: left;
}

.time-text {
  font-size: 26rpx;
  color: #999;
}

/* 底部按钮 */
.popup-footer {
  padding: 24rpx 30rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
  width: 100%;
  height: 76rpx;
  background: linear-gradient(135deg, #2492F2 0%, #1976D2 100%);
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 38rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-btn:active {
  transform: scale(0.98);
  transition: transform 0.1s;
}
