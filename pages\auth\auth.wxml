<view class="auth-contain">
  <t-image src="{{logoUrl}}" width="90" height="90" shape="circle" aria-label="一个放置在墙角的黄色行李箱" />
  <text class="auth-contain_name">{{name}}</text>

  <view class="auth-contain_tip">
    <view style="color: #444444;font-size: 28rpx;margin-top: 70rpx;">该程序将获取以下信息</view>
    <view style="color: #999999;font-size: 24rpx;margin-top: 20rpx;">· 获得您的手机号码作为唯一账号</view>
  </view>

  <button wx:if="{{hasRegister}}" class="auth-contain_login_btn {{isAgree?'':'login-disabled'}}" bind:tap="wxLogin">快捷登录/注册</button>
  <button wx:else class="auth-contain_login_btn {{isAgree?'':'login-disabled'}}" open-type="getPhoneNumber" bindgetphonenumber="wxPhoneLogin">手机号快捷登录</button>
  <!-- <view class="auth-contain_login_btn {{isAgree?'':'login-disabled'}}" bind:tap="doLogin">微信授权登录/注册</view> -->
  <view class="auth-contain_cancel_btn" bind:tap="back">暂不登录</view>

  <view style="display: flex;font-size: 24rpx;line-height: 48rpx;margin-top: 30rpx;">
    <t-radio default-checked="{{isAgree}}" model:value="{{isAgree}}" label="" allow-uncheck bind:change="onChangeRadio" />
    我已阅读并同意
    <view style="font-size: 24rpx;color: var(--themeColor);" bind:tap="toUserWeb">《用户服务协议》</view>
    和
    <view style="font-size: 24rpx;color: var(--themeColor);" bind:tap="toYsWeb">《隐私协议》</view>
  </view>
</view>