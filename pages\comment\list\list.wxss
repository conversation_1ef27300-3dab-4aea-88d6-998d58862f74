/* pages/comment/list/list.wxss */
.comment-list-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 评价头部区域 */
.comment-header-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

/* 评价总分区域 */
.rating-summary {
  padding: 20rpx;
}

.rating-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.lineHeighe{
  height: 20rpx;
  background-color: #f5f5f5;
}

.average-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #2492F2;
}

.rating-stars {
  display: flex;
  align-items: center;
}

.total-count {
  font-size: 26rpx;
  color: #666;
}

/* 评价列表 */
.comment-list {
  padding: 0 20rpx;
}

.comment-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.user-avatar-img {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.avatar-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.rating-stars {
  margin-left: 20rpx;
}

/* 评价内容 */
.comment-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

/* 商品信息 */
.goods-info {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.goods-name {
  font-size: 26rpx;
  color: #666;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #999;
}

/* 没有更多 */
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #ccc;
}
