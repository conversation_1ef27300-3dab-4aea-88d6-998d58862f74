Component({
  properties: {
    // 初始位置（相对屏幕左上角）
    initX: {
      type: Number,
      value: 600,
    },
    initY: {
      type: Number,
      value: 700,
    },
  },
  data: {
    x: 600,
    y: 700,
  },
  lifetimes: {
    attached() {
      // 使用初始值设置位置
      const { initX, initY } = this.properties;
      this.setData({ x: initX, y: initY });
    }
  },
  methods: {
    onTap() {
      // 优先使用企业微信客服
      if (wx.openCustomerServiceChat && typeof wx.openCustomerServiceChat === 'function') {
        wx.openCustomerServiceChat({
          extInfo: { url: 'https://work.weixin.qq.com/kfid/kfc14af0eb0453e516a' },
          corpId: 'ww36fad4ed63da23c7',
          success: () => {},
          fail: () => {
            this.fallback();
          }
        });
      } else {
        this.fallback();
      }
    },
    fallback() {
      wx.showActionSheet({
        itemList: ['拨打客服电话', '查看联系方式'],
        success: (res) => {
          if (res.tapIndex === 0) {
            wx.makePhoneCall({ phoneNumber: '18975150553' });
          } else if (res.tapIndex === 1) {
            wx.showModal({
              title: '客服联系方式',
              content: '客服电话：18975150553\r\n工作时间：9:00-18:00',
              showCancel: false,
              confirmText: '知道了'
            });
          }
        }
      });
    }
  }
});