/* pages/comment/add/add.wxss */
.comment-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 商品信息 */
.goods-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.goods-item {
  display: flex;
  align-items: flex-start;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.goods-spec {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.goods-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}

/* 评分区域 */
.rating-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 30rpx;
}

.rating-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 0;
}

.rating-text {
  font-size: 28rpx;
  color: #2492F2;
  font-weight: 500;
  min-width: 120rpx;
  text-align: center;
}

.rating-text text {
  transition: all 0.3s ease;
}

/* 评价内容 */
.content-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.content-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

.content-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

/* 提交按钮 */
.submit-section {
  padding: 0 20rpx 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
}

.submit-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.submit-btn:not(.loading):active {
  transform: scale(0.98);
  transition: transform 0.1s;
}
