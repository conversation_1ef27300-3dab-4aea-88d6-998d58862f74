<!--pages/search/index.wxml-->
<wxs module="utils">
  var mainPic = function (pic) {
    return pic.split(",")[0];
  }
  var getPrice = function (goods) {
    if (goods.specType === 1) {
      return goods.goodsItemList[0].price;
    } else if (goods.specType === 2) {
      var price = 0;
      for (var i = 0; i < goods.goodsItemList.length; i++) {
        if (price == 0 || price > goods.goodsItemList[i].price) {
          price = goods.goodsItemList[i].price;
        }
      }
      return price;
    }
    return 0;
  }
  module.exports = { mainPic: mainPic, getPrice: getPrice };
</wxs>

<view class="search-page">
  <!-- 顶部搜索区域 -->
  <view class="search-header">
    <view class="search-input-wrapper">
      <t-icon class="search-icon" name="search" size="32rpx" color="#999999" />
      <input
        class="search-input"
        placeholder="搜索商品或服务"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        confirm-type="search"
        focus="{{inputFocus}}"
        placeholder-class="search-placeholder"
      />
      <t-icon
        class="clear-icon"
        name="close-circle-filled"
        size="32rpx"
        color="#cccccc"
        wx:if="{{searchKeyword}}"
        bind:tap="clearSearch"
      />
    </view>
    <view class="search-btn" bind:tap="onSearch">确定</view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{searchHistory.length > 0 && !hasSearched}}">
    <view class="history-header">
      <text class="history-title">历史搜索</text>
      <t-icon name="delete" size="32rpx" color="#999999" bind:tap="clearHistory" />
    </view>
    <view class="history-tags">
      <view 
        class="history-tag" 
        wx:for="{{searchHistory}}" 
        wx:key="index"
        bind:tap="selectHistory"
        data-keyword="{{item}}"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 排序筛选栏 -->
  <view class="sort-bar" wx:if="{{hasSearched && currentGoodsList.length > 0}}">
    <view 
      class="sort-item {{sortType === 'default' ? 'active' : ''}}" 
      bind:tap="onSort" 
      data-type="default"
    >
      综合
    </view>
    <view 
      class="sort-item {{sortType === 'latest' ? 'active' : ''}}" 
      bind:tap="onSort" 
      data-type="latest"
    >
      最新
    </view>
    <view 
      class="sort-item {{sortType === 'price' ? 'active' : ''}}" 
      bind:tap="onSort" 
      data-type="price"
    >
      价格
      <t-icon 
        name="{{priceOrder === 'asc' ? 'chevron-up' : 'chevron-down'}}" 
        size="24rpx" 
        color="{{sortType === 'price' ? '#2492F2' : '#999999'}}"
      />
    </view>
    <view 
      class="sort-item {{sortType === 'sales' ? 'active' : ''}}" 
      bind:tap="onSort" 
      data-type="sales"
    >
      销量
    </view>
  </view>

  <!-- 商品热搜榜标题 -->
  <view class="recommend-title" wx:if="{{!hasSearched}}">
    <image class="hot-icon" src="/static/images/hot.png" mode="aspectFit" />
    <text class="title-text">商品热搜榜</text>
  </view>

  <!-- 商品热搜榜 - 初始化时显示 -->
  <view class="goods-list" wx:if="{{!hasSearched}}">
    <view
      class="goods-item"
      wx:for="{{currentGoodsList}}"
      wx:key="id"
      bind:tap="toGoodsDetail"
      data-goods="{{item}}"
    >
      <view class="goods-image-container">
        <image class="goods-image" src="{{utils.mainPic(item.pic)}}" mode="widthFix" />
      </view>
      <view class="goods-info">
        <view class="goods-title">{{item.name || item.title}}</view>
      </view>
    </view>
  </view>

  <!-- 搜索结果 - 使用index-goods组件展示 -->
  <view wx:if="{{hasSearched && currentGoodsList.length > 0}}">
    <index-goods goodsList="{{currentGoodsList}}" bind:toAddGoods="toAddGoods"></index-goods>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{hasSearched && currentGoodsList.length === 0}}">
    <t-icon name="search" size="120rpx" color="#CCCCCC" />
    <text class="empty-text">未找到相关商品</text>
    <text class="empty-tip">试试其他关键词吧</text>
  </view>
</view>

<!-- 商品规格选择弹窗 -->
<goods-specs-popup
  id="goodsSpecsPopup"
  bind:doBuyNow="doBuyNow"
  bind:doAddCart="doAddCart"
  goodsInfo="{{goodsInfo}}"
  show="{{isSpuSelectPopupShow}}"
  bind:closeSpecsPopup="handlePopupHide">
</goods-specs-popup>
