/* pages/order/orderDetail/orderDetail.wxss */
page {
  background-color: #f5f5f5;
  height: calc(100vh - env(safe-area-inset-bottom));
}

page .divider-line {
  width: 100%;
  height: 20rpx;
  background-color: #f5f5f5;
}

.goods-info__img {
  width: 130rpx;
  height: 130rpx;
}

.goods-info__img .goods-info__img_v {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.goods-info__content {
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0rpx 20rpx;
}

.goods-info__content .goods-info__title {
  flex-shrink: 0;
  font-size: 30rpx;
  color: #333;
  line-height: 40rpx;
  font-weight: 600;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-line-clamp: 2;
}

.goods-info__content .goods-info__desc {
  flex-shrink: 0;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-line-clamp: 1;
}

.goods-info__spec {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.goods-info__right_content {
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.goods-info__right_content .goods-info__price {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 32rpx;
}

.goods-info__num {
  font-size: 26rpx;
  color: #777;
  text-align: right;
  margin-top: 10rpx;
  color: #999;
}

/* 商品项可点击样式 */
.goods-item-clickable {
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.goods-item-clickable:active {
  background-color: #f8f8f8;
}


.bottom_submit_layout {
  width: 100%;
  height: 110rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: env(safe-area-inset-bottom);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  box-sizing: border-box;
}

.bottom_submit_layout .order-list-btn {
  width: 150rpx;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 26rpx;
  margin-right: 30rpx;
}

.remark-class {
  --td-font-size-base: 26rpx;
  --td-font-size-m: 30rpx;
  --td-input-label-text-color: #999;
  --td-input-vertical-padding: 20rpx 0rpx;
}

.remark-class .t-input__wrap .t-input__content {
  align-items: top;
  min-height: 96rpx;
}

.order-item-calc {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-end;
  margin-top: 12rpx;
  margin-bottom: 12rpx;
}

.order-item-calc .order-item-calc-1 {
  color: #999;
  font-size: 26rpx;
  margin-right: 20rpx;
}

.order-item-calc .order-item-calc-2 {
  color: #555;
  font-size: 26rpx;
}

.order-item-header-no {
  flex: 1;
  color: #666;
  font-size: 28rpx;
}

.order-item-header-status {
  color: #fa4126;
  font-size: 28rpx;
}

.label-layout {
  color: #666;
  padding: 20rpx 0;
  justify-content: space-between;
}

/* 线下支付信息弹窗样式 */
.offline-payment-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx;
  box-sizing: border-box;
}

.offline-payment-content {
  background: #fff;
  border-radius: 16rpx;
  width: 100%;
  max-width: 680rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.offline-payment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.offline-payment-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.copy-btn {
  flex: 1;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid var(--themeColor, #1989fa);
  color: var(--themeColor, #1989fa);
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.copy-btn:active {
  background: var(--themeColor, #1989fa);
  color: #fff;
}

.offline-payment-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  font-size: 32rpx;
  font-weight: bold;
}

.offline-payment-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.offline-payment-greeting {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
  line-height: 1;
}

.offline-payment-description {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.offline-payment-notice {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-top: 32rpx;
  margin-bottom: 24rpx;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid var(--themeColor, #1989fa);
}

.offline-payment-thanks {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.5;
}

.payment-info-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.payment-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

.offline-payment-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 24rpx;
}

.confirm-btn {
  flex: 1;
  height: 88rpx;
  background: var(--themeColor, #1989fa);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

/* 发票服务卡片 - 复杂优化样式 */
.invoice-service-card {
  margin-top: 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
  position: relative;
}

.invoice-service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #edbf46 0%, #f4d03f 50%, #edbf46 100%);
}

/* 卡片头部 */
.invoice-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.invoice-header-left {
  display: flex;
  align-items: center;
}

.invoice-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  border: 2rpx solid #ffeaa7;
}

.invoice-header-info {
  display: flex;
  flex-direction: column;
}

.invoice-card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.invoice-card-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 状态徽章 */
.invoice-status-badge {
  position: relative;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff7e6;
  color: #faad14;
  border: 1rpx solid #ffd666;
}

.status-processing {
  background: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  background: #faad14;
}

.status-dot-processing {
  background: #1890ff;
  animation: pulse 2s infinite;
}

.status-dot-completed {
  background: #52c41a;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 发票信息区域 */
.invoice-info-section {
  padding: 24rpx 32rpx;
  background: #fafbfc;
  border-bottom: 1rpx solid #f0f0f0;
}

.invoice-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.invoice-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.info-grid-item {
  display: flex;
  flex-direction: column;
  padding: 16rpx;
  background: #fff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
}

.info-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.price-value {
  color: #fa4126 !important;
  font-weight: 600 !important;
}

/* 操作按钮区域 */
.invoice-actions-section {
  padding: 24rpx 32rpx;
}

.invoice-action-card {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.invoice-action-card:last-child {
  margin-bottom: 0;
}

.primary-action {
  background: linear-gradient(135deg, #edbf46 0%, #f4d03f 100%);
  border: 1rpx solid #edbf46;
  box-shadow: 0 4rpx 16rpx rgba(237, 191, 70, 0.3);
}

.primary-action:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(237, 191, 70, 0.4);
}

.secondary-action {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border: 1rpx solid #edbf46;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.secondary-action:active {
  background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
  transform: translateY(2rpx);
}

.tertiary-action {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
  border: 1rpx solid #d9f7be;
  box-shadow: 0 2rpx 12rpx rgba(82, 196, 26, 0.1);
}

.tertiary-action:active {
  background: linear-gradient(135deg, #f0f9e8 0%, #e6f7d3 100%);
  transform: translateY(2rpx);
}

.action-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.secondary-icon {
  background: rgba(237, 191, 70, 0.1);
}

.tertiary-icon {
  background: rgba(82, 196, 26, 0.1);
}

.action-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.primary-action .action-title {
  color: #fff;
}

.action-desc {
  font-size: 24rpx;
  color: #666;
}

.primary-action .action-desc {
  color: rgba(255, 255, 255, 0.8);
}

.action-arrow {
  margin-left: 16rpx;
}

/* 温馨提示区域 */
.invoice-tips {
  padding: 24rpx 32rpx 32rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border-top: 1rpx solid #e6f3ff;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1890ff;
}

.tips-content {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.tips-content:last-child {
  margin-bottom: 0;
}

/* 发票按钮优化样式 - 保持兼容 */
.invoice-detail-btn {
  border-radius: 25rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.invoice-detail-btn-primary {
  background: linear-gradient(135deg, #edbf46 0%, #f4d03f 100%);
  color: #fff;
  border: 1rpx solid #edbf46;
}

.invoice-detail-btn-primary:active {
  background: linear-gradient(135deg, #d4ac0d 0%, #edbf46 100%);
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(237, 191, 70, 0.4);
}

.invoice-detail-btn-secondary {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  color: #edbf46;
  border: 1rpx solid #edbf46;
}

.invoice-detail-btn-secondary:active {
  background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
  transform: translateY(1rpx);
}