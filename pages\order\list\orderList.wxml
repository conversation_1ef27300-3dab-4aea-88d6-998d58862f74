<!--pages/order/list/orderList.wxml-->
<view class="flex-column">
  <t-tabs value="{{tabIndex}}" bind:change="onTabsChange">
    <t-tab-panel label="全部" value="0" />
    <t-tab-panel label="待付款" value="1" />
    <t-tab-panel label="待发货" value="2" />
    <!-- <t-tab-panel label="全部" value="0" badge-props="{{ { dot: true, offset: ['4px', '4px'] } }}" />
    <t-tab-panel label="待付款" value="1" badge-props="{{ { dot: true, offset: ['4px', '4px'] } }}" />
    <t-tab-panel label="待发货" value="2" badge-props="{{ { count: 8, offset: ['8px', '8px'] } }}" /> -->
    <t-tab-panel label="待收货" value="3" />
    <t-tab-panel label="已完成" value="4" />
  </t-tabs>

  <refresher-loader id="refresherLoader" wr-class="refresher-loader-order" dataList="{{orderList}}" bind:onRefresh="onRefresh">
    <view class="order-item-layout" wx:for="{{orderList}}" wx:key="orderCode" data-item="{{item}}" catch:tap="toDetail">
      <view class="order-item-header">
        <text class="order-item-header-no">订单号 {{item.orderCode}}</text>
        <!-- 优先显示退款状态 -->
        <text wx:if="{{item.orderAfterSales && item.orderAfterSales.serviceStatus === 'completed' && item.orderAfterSales.auditResult === 'agree'}}" style="color: #52C41A;" class="order-item-header-status">已退款</text>
        <!-- 其他订单状态 -->
        <text wx:elif="{{item.orderStatus === 0}}" style="color: #FF9500;" class="order-item-header-status">待支付</text>
        <text wx:elif="{{item.orderStatus === 11}}" style="color: #3498DB;" class="order-item-header-status">待发货</text>
        <text wx:elif="{{item.orderStatus === 21}}" style="color: #F1C40F;" class="order-item-header-status">待收货</text>
        <text wx:elif="{{item.orderStatus === 50}}" style="color: #27AE60;" class="order-item-header-status">已完成</text>
        <text wx:elif="{{item.orderStatus === 1 || item.orderStatus === 12}}" style="color: #7F8C8D;" class="order-item-header-status">已取消</text>
      </view>

      <view class="flex-row" style="margin-top: 30rpx;" wx:for="{{item.orderItemList}}" wx:for-item="goods" wx:key="id">
        <view class="order-item-goods-info__img">
          <!-- <t-image t-class="order-item-goods-info__img_v" shape="round" src="{{goods.pic}}" lazy /> -->
          <image class="order-item-goods-info__img_v" src="{{goods.pic}}" />
        </view>
        <view class="order-item-goods-info__content">
          <view class="order-item-goods-info__title">{{goods.goodsName}}</view>
          <view class="order-item-goods-info__desc">规格: {{ goods.specDesc }}</view>
        </view>

        <view class="order-item-goods-info__right_content">
          <view class="order-item-goods-info__price">
            <price price="{{goods.price*100}}" />
          </view>
          <view class="order-item-goods-info__num">x {{goods.buyNum}}</view>
        </view>
      </view>

      <view class="order-item-remark lines-1">备注: {{item.remark||"无"}}</view>
      <view class="order-item-calc">
        <!-- <view class="order-item-calc-1">
          <text>总价 </text>
          <price price="{{item.orderPrice*100}}" fill decimalSmaller />
        </view> -->
        <!-- <view class="order-item-calc-1">
          <text>运费 </text>
          <price price="{{item.deliveryPrice*100}}" fill decimalSmaller />
        </view> -->
       
      </view>

      <!-- 总价与评价按钮水平布局 -->
      <view class="price-action-layout" wx:if="{{item.orderStatus == 50}}">
        <view class="total-price-section">
          <text class="total-label">总价: </text>
          <price style="color: #fa4126;font-weight: bold;font-size: 30rpx;" price="{{item.orderPayPrice*100}}" />
        </view>
        <view class="comment-action">
          <view data-item="{{item}}" catch:tap="{{item.hasComment ? 'doViewComment' : 'doAddComment'}}" class="theme-btn order-list-btn" hover-class="hover-btn">
            {{item.hasComment ? '查看评价' : '待评价'}}
          </view>
        </view>
      </view>

      <!-- 其他状态的按钮布局 -->
      <view class="flex-row btn-layout" wx:if="{{item.orderStatus != 50}}">
        <view wx:if="{{item.orderStatus == 0 || item.orderStatus == 11}}" data-item="{{item}}" catch:tap="doCancelOrder" class="outline-btn order-list-btn" hover-class="hover-btn">取消订单</view>
        <view wx:if="{{item.orderStatus == 0}}" data-item="{{item}}" catch:tap="doPay" style="margin-left: 30rpx;" class="red-btn order-list-btn" hover-class="hover-btn">付款</view>
        <!-- 退款相关按钮 -->
        <!-- 申请退款和撤销申请只在待收货状态显示 -->
        <view wx:if="{{item.orderStatus == 21}}">
          <!-- 未申请退款 -->
          <view wx:if="{{!item.orderAfterSales}}" data-item="{{item}}" catch:tap="doApplyRefund" class="refund-btn order-list-btn" hover-class="hover-btn">申请退款</view>
          <!-- 已申请退款，待审核（用户申请状态或平台审核状态） -->
          <view wx:elif="{{item.orderAfterSales.serviceStatus == 'user_apply' || item.orderAfterSales.serviceStatus == 'platform_audit'}}" data-item="{{item}}" catch:tap="doCancelAfterSales" class="cancel-refund-btn order-list-btn" hover-class="hover-btn">撤销申请</view>
        </view>

        <!-- 审核结果按钮不受订单状态限制 -->
        <view wx:if="{{item.orderAfterSales && item.orderAfterSales.serviceStatus == 'completed'}}" data-item="{{item}}" catch:tap="doViewAuditResult" class="audit-result-btn order-list-btn" hover-class="hover-btn">
          查看结果
        </view>
        <view wx:if="{{item.orderStatus == 21}}" data-item="{{item}}" catch:tap="doTakeGoods" style="margin-left: 30rpx;" class="theme-btn order-list-btn" hover-class="hover-btn">确认收货</view>
      </view>

    </view>
  </refresher-loader>

  <!-- 评价详情弹窗 -->
  <comment-detail-popup
    show="{{showCommentPopup}}"
    comment="{{currentComment}}"
    orderInfo="{{currentOrderInfo}}"
    bind:close="onCloseCommentPopup"
  />

  <!-- 审核结果弹窗 -->
  <audit-result-popup
    visible="{{auditPopupVisible}}"
    audit-result="{{currentAuditResult.auditResult}}"
    audit-remark="{{currentAuditResult.auditRemark}}"
    audit-time="{{currentAuditResult.auditTime}}"
    order-code="{{currentAuditResult.orderCode}}"
    bind:close="onCloseAuditPopup"
    bind:retry="onRetryRefund"
  />

  <!-- 支付方式选择动作面板 -->
  <van-action-sheet
    show="{{ showPaymentActionSheet }}"
    actions="{{ paymentActions }}"
    bind:select="onSelectPaymentMethod"
    bind:close="onClosePaymentActionSheet"
    title="选择支付方式"
    cancel-text="取消"
    close-on-click-action="{{ true }}"
  />

  <!-- 线下支付信息遮罩层 -->
  <van-overlay
    show="{{ showOfflinePaymentOverlay }}"
    bind:click="closeOfflinePaymentInfo"
    z-index="1000"
  >
    <view class="offline-payment-wrapper" catch:tap="stopPropagation">
      <view class="offline-payment-content">
        <view class="offline-payment-header">
          <text class="offline-payment-title">线下支付信息</text>
          <view class="offline-payment-close" bind:tap="closeOfflinePaymentInfo">
            <text>×</text>
          </view>
        </view>

        <view class="offline-payment-body">
          <text class="offline-payment-greeting">您好！</text>

          <text class="offline-payment-description">以下是本平台的对公账户信息，烦请您妥善留存并用于公对公付款：</text>

          <view class="payment-info-item">
            <text class="info-label">账户名称:</text>
            <text class="info-value">长沙耀楼产业互联科技服务有限公司</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">账户号码:</text>
            <text class="info-value">731912140110006</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">开户银行:</text>
            <text class="info-value">招商银行股份有限公司长沙晓园支行</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">纳税识别号：</text>
            <text class="info-value">91430104MAEBFJ0G7C</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">公司地址:</text>
            <text class="info-value">湖南省长沙市岳麓区岳麓街道溁左路中南大学科技园研发总部 6 栋 322 房</text>
          </view>

          <text class="offline-payment-notice">在您完成公对公付款后，请及时保留好付款凭证。平台会在第一时间对付款信息进行核实，确认无误后，将迅速为您处理订单，全力保障您的业务顺利推进。</text>

          <text class="offline-payment-thanks">如有任何疑问，欢迎随时与我们联系。</text>
          <text class="offline-payment-thanks">感谢您的信任与支持 🌹</text>
        </view>

        <view class="offline-payment-footer">
          <view class="copy-btn" bind:tap="copyPaymentInfo">
            <text>一键复制</text>
          </view>
          <view class="confirm-btn" bind:tap="closeOfflinePaymentInfo">
            <text>我知道了</text>
          </view>
        </view>
      </view>
    </view>
  </van-overlay>
</view>