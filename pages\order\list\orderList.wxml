<!--pages/order/list/orderList.wxml-->
<view class="flex-column">
  <t-tabs value="{{tabIndex}}" bind:change="onTabsChange">
    <t-tab-panel label="全部" value="0" />
    <t-tab-panel label="待付款" value="1" />
    <t-tab-panel label="待发货" value="2" />
    <!-- <t-tab-panel label="全部" value="0" badge-props="{{ { dot: true, offset: ['4px', '4px'] } }}" />
    <t-tab-panel label="待付款" value="1" badge-props="{{ { dot: true, offset: ['4px', '4px'] } }}" />
    <t-tab-panel label="待发货" value="2" badge-props="{{ { count: 8, offset: ['8px', '8px'] } }}" /> -->
    <t-tab-panel label="待收货" value="3" />
    <t-tab-panel label="已完成" value="4" />
  </t-tabs>

  <refresher-loader id="refresherLoader" wr-class="refresher-loader-order" dataList="{{orderList}}" bind:onRefresh="onRefresh">
    <!-- 订单卡片 -->
    <view class="order-card" wx:for="{{orderList}}" wx:key="orderCode" data-item="{{item}}" catch:tap="toDetail">
      <!-- 卡片头部 -->
      <view class="order-card-header">
        <view class="order-header-left">
          <view class="order-icon-wrapper">
            <t-icon name="shop" size="20" style="color: #edbf46;" />
          </view>
          <view class="order-header-info">
            <text class="order-number">{{item.orderCode}}</text>
            <text class="order-time">{{item.createTime}}</text>
          </view>
        </view>
        <!-- 订单状态徽章 -->
        <view class="order-status-badge">
          <!-- 优先显示退款状态 -->
          <view wx:if="{{item.orderAfterSales && item.orderAfterSales.serviceStatus === 'completed' && item.orderAfterSales.auditResult === 'agree'}}" class="status-badge status-refunded">
            <view class="status-dot status-dot-refunded"></view>
            <text class="status-text">已退款</text>
          </view>
          <!-- 其他订单状态 -->
          <view wx:elif="{{item.orderStatus === 0}}" class="status-badge status-pending">
            <view class="status-dot status-dot-pending"></view>
            <text class="status-text">待支付</text>
          </view>
          <view wx:elif="{{item.orderStatus === 11}}" class="status-badge status-shipping">
            <view class="status-dot status-dot-shipping"></view>
            <text class="status-text">待发货</text>
          </view>
          <view wx:elif="{{item.orderStatus === 21}}" class="status-badge status-receiving">
            <view class="status-dot status-dot-receiving"></view>
            <text class="status-text">待收货</text>
          </view>
          <view wx:elif="{{item.orderStatus === 50}}" class="status-badge status-completed">
            <view class="status-dot status-dot-completed"></view>
            <text class="status-text">已完成</text>
          </view>
          <view wx:elif="{{item.orderStatus === 1 || item.orderStatus === 12}}" class="status-badge status-cancelled">
            <view class="status-dot status-dot-cancelled"></view>
            <text class="status-text">已取消</text>
          </view>
        </view>
      </view>

      <!-- 商品列表区域 -->
      <view class="goods-section">
        <view class="goods-item" wx:for="{{item.orderItemList}}" wx:for-item="goods" wx:key="id">
          <view class="goods-image-wrapper">
            <image class="goods-image" src="{{goods.pic}}" />
          </view>
          <view class="goods-info">
            <text class="goods-name">{{goods.goodsName}}</text>
            <text class="goods-spec">规格: {{goods.specDesc}}</text>
          </view>
          <view class="goods-price-info">
            <price class="goods-price" price="{{goods.price*100}}" />
            <text class="goods-quantity">x{{goods.buyNum}}</text>
          </view>
        </view>
      </view>

      <!-- 订单信息区域 -->
      <view class="order-info-section">
        <view class="order-remark">
          <t-icon name="chat-bubble" size="14" style="color: #999; margin-right: 8rpx;" />
          <text class="remark-text">备注: {{item.remark || "无"}}</text>
        </view>
      </view>

      <!-- 卡片底部操作区域 -->
      <view class="order-card-footer">
        <!-- 总价显示 - 只在支付成功时显示 -->
        <view wx:if="{{item.orderStatus != 0}}" class="total-price-wrapper">
          <text class="total-label">总价</text>
          <price class="total-price" price="{{item.orderPayPrice*100}}" />
        </view>

        <!-- 操作按钮区域 -->
        <view class="card-actions">
          <!-- 已完成订单的操作 -->
          <view wx:if="{{item.orderStatus == 50}}" class="completed-actions">
            <!-- 评价按钮 -->
            <view data-item="{{item}}" catch:tap="{{item.hasComment ? 'doViewComment' : 'doAddComment'}}" class="action-btn primary-btn" hover-class="hover-btn">
              {{item.hasComment ? '查看评价' : '待评价'}}
            </view>

            <!-- 开具发票按钮 -->
            <view wx:if="{{!item.orderInvoiceList || item.orderInvoiceList.length == 0 || item.orderInvoiceList[0].status == 0}}"
                  data-item="{{item}}"
                  catch:tap="doTakeInvoice"
                  class="action-btn invoice-btn"
                  hover-class="hover-btn">
              开具发票
            </view>

            <!-- 下载发票按钮 -->
            <view wx:if="{{item.orderInvoiceList && item.orderInvoiceList.length > 0 && item.orderInvoiceList[0].status == 1}}"
                  data-item="{{item}}"
                  catch:tap="doSaveInvoice"
                  class="action-btn invoice-btn"
                  hover-class="hover-btn">
              下载发票
            </view>
          </view>

          <!-- 其他状态的操作按钮 -->
          <view wx:if="{{item.orderStatus != 50}}" class="other-actions">
            <!-- 取消订单 -->
            <view wx:if="{{item.orderStatus == 0 || item.orderStatus == 11}}"
                  data-item="{{item}}"
                  catch:tap="doCancelOrder"
                  class="action-btn secondary-btn"
                  hover-class="hover-btn">
              取消订单
            </view>

            <!-- 付款按钮 -->
            <view wx:if="{{item.orderStatus == 0}}"
                  data-item="{{item}}"
                  catch:tap="doPay"
                  class="action-btn primary-btn"
                  hover-class="hover-btn">
              立即付款
            </view>

            <!-- 退款相关按钮 -->
            <view wx:if="{{item.orderStatus == 21}}">
              <!-- 未申请退款 -->
              <view wx:if="{{!item.orderAfterSales}}"
                    data-item="{{item}}"
                    catch:tap="doApplyRefund"
                    class="action-btn warning-btn"
                    hover-class="hover-btn">
                申请退款
              </view>
              <!-- 已申请退款，待审核 -->
              <view wx:elif="{{item.orderAfterSales.serviceStatus == 'user_apply' || item.orderAfterSales.serviceStatus == 'platform_audit'}}"
                    data-item="{{item}}"
                    catch:tap="doCancelAfterSales"
                    class="action-btn danger-btn"
                    hover-class="hover-btn">
                撤销申请
              </view>
            </view>

            <!-- 审核结果按钮 -->
            <view wx:if="{{item.orderAfterSales && item.orderAfterSales.serviceStatus == 'completed'}}"
                  data-item="{{item}}"
                  catch:tap="doViewAuditResult"
                  class="action-btn info-btn"
                  hover-class="hover-btn">
              查看结果
            </view>

            <!-- 确认收货 -->
            <view wx:if="{{item.orderStatus == 21}}"
                  data-item="{{item}}"
                  catch:tap="doTakeGoods"
                  class="action-btn primary-btn"
                  hover-class="hover-btn">
              确认收货
            </view>
          </view>
        </view>
      </view>
    </view>
  </refresher-loader>

  <!-- 评价详情弹窗 -->
  <comment-detail-popup
    show="{{showCommentPopup}}"
    comment="{{currentComment}}"
    orderInfo="{{currentOrderInfo}}"
    bind:close="onCloseCommentPopup"
  />

  <!-- 审核结果弹窗 -->
  <audit-result-popup
    visible="{{auditPopupVisible}}"
    audit-result="{{currentAuditResult.auditResult}}"
    audit-remark="{{currentAuditResult.auditRemark}}"
    audit-time="{{currentAuditResult.auditTime}}"
    order-code="{{currentAuditResult.orderCode}}"
    bind:close="onCloseAuditPopup"
    bind:retry="onRetryRefund"
  />

  <!-- 支付方式选择动作面板 -->
  <van-action-sheet
    show="{{ showPaymentActionSheet }}"
    actions="{{ paymentActions }}"
    bind:select="onSelectPaymentMethod"
    bind:close="onClosePaymentActionSheet"
    title="选择支付方式"
    cancel-text="取消"
    close-on-click-action="{{ true }}"
  />

  <!-- 线下支付信息遮罩层 -->
  <van-overlay
    show="{{ showOfflinePaymentOverlay }}"
    bind:click="closeOfflinePaymentInfo"
    z-index="1000"
  >
    <view class="offline-payment-wrapper" catch:tap="stopPropagation">
      <view class="offline-payment-content">
        <view class="offline-payment-header">
          <text class="offline-payment-title">线下支付信息</text>
          <view class="offline-payment-close" bind:tap="closeOfflinePaymentInfo">
            <text>×</text>
          </view>
        </view>

        <view class="offline-payment-body">
          <text class="offline-payment-greeting">您好！</text>

          <text class="offline-payment-description">以下是本平台的对公账户信息，烦请您妥善留存并用于公对公付款：</text>

          <view class="payment-info-item">
            <text class="info-label">账户名称:</text>
            <text class="info-value">长沙耀楼产业互联科技服务有限公司</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">账户号码:</text>
            <text class="info-value">731912140110006</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">开户银行:</text>
            <text class="info-value">招商银行股份有限公司长沙晓园支行</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">纳税识别号：</text>
            <text class="info-value">91430104MAEBFJ0G7C</text>
          </view>

          <view class="payment-info-item">
            <text class="info-label">公司地址:</text>
            <text class="info-value">湖南省长沙市岳麓区岳麓街道溁左路中南大学科技园研发总部 6 栋 322 房</text>
          </view>

          <text class="offline-payment-notice">在您完成公对公付款后，请及时保留好付款凭证。平台会在第一时间对付款信息进行核实，确认无误后，将迅速为您处理订单，全力保障您的业务顺利推进。</text>

          <text class="offline-payment-thanks">如有任何疑问，欢迎随时与我们联系。</text>
          <text class="offline-payment-thanks">感谢您的信任与支持 🌹</text>
        </view>

        <view class="offline-payment-footer">
          <view class="copy-btn" bind:tap="copyPaymentInfo">
            <text>一键复制</text>
          </view>
          <view class="confirm-btn" bind:tap="closeOfflinePaymentInfo">
            <text>我知道了</text>
          </view>
        </view>
      </view>
    </view>
  </van-overlay>
</view>