/* pages/order/list/orderList.wxss */
page {
  background-color: #f5f5f5;
  overflow: hidden;
  height: calc(100vh - env(safe-area-inset-bottom));
}

.refresher-loader-order {
  height: calc(100vh - env(safe-area-inset-bottom) - 100rpx) !important;
}

.order-item-layout {
  margin-top: 20rpx;
  padding: 20rpx 30rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.order-item-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 60rpx;
}

.order-item-header .order-item-header-no {
  flex: 1;
  color: #666;
  font-size: 28rpx;
}

.order-item-header .order-item-header-status {
  color: #fa4126;
  font-size: 28rpx;
}

.order-item-goods-info__img {
  width: 130rpx;
  height: 130rpx;
}

.order-item-goods-info__img .order-item-goods-info__img_v {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.order-item-goods-info__content {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0rpx 20rpx;
}

.order-item-goods-info__content .order-item-goods-info__title {
  flex-shrink: 0;
  font-size: 30rpx;
  color: #333;
  line-height: 40rpx;
  font-weight: 600;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-line-clamp: 2;
}

.order-item-goods-info__content .order-item-goods-info__desc {
  margin-top: 10rpx;
  color: #999;
  font-size: 24rpx;
}

.order-item-goods-info__right_content {
  display: flex;
  flex-direction: column;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.order-item-goods-info__right_content .order-item-goods-info__price {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 26rpx;
  color: #666;
}

.order-item-goods-info__right_content .order-item-goods-info__num {
  font-size: 26rpx;
  text-align: right;
  margin-top: 10rpx;
  color: #999;
}

.order-item-remark {
  font-size: 24rpx;
  margin-top: 12rpx;
  margin-left: 6rpx;
  color: #666;
}

.order-item-calc {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-end;
  margin-top: 12rpx;
}

.order-item-calc .order-item-calc-1 {
  color: #999;
  font-size: 26rpx;
  margin-right: 20rpx;
}

.order-item-calc .order-item-calc-2 {
  color: #555;
  font-size: 26rpx;
}

.btn-layout {
  justify-content: right;
  margin-top: 24rpx;
  justify-content: flex-end;
}

.btn-layout .order-list-btn {
  width: 150rpx;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 26rpx;
}

/* 总价与评价按钮水平布局 */
.price-action-layout {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24rpx;
  padding: 0 4rpx;
}

.total-price-section {
  display: flex;
  align-items: center;
}

.total-label {
  color: #555;
  font-size: 26rpx;
  margin-right: 8rpx;
}

.comment-action .order-list-btn {
  width: 150rpx;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 26rpx;
}

.wr-refresher {
  height: calc(100vh - 96rpx) !important;
}

/* 申请退款按钮样式覆盖 */
.refund-btn {
  border-radius: 999rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f5f5;
  border: 2rpx solid #f5f5f5;
  text-align: center;
}

/* 撤销申请按钮样式 */
.cancel-refund-btn {
  border-radius: 999rpx;
  font-size: 26rpx;
  color: #ff6b6b;
  background-color: #fff5f5;
  border: 2rpx solid #ffebee;
  text-align: center;
}

/* 审核结果按钮样式 */
.audit-result-btn {
  border-radius: 999rpx;
  font-size: 26rpx;
  color: #2492F2;
  background-color: #f0f8ff;
  border: 2rpx solid #e6f3ff;
  text-align: center;
}

/* 线下支付信息弹窗样式 */
.offline-payment-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx;
  box-sizing: border-box;
}

.offline-payment-content {
  background: #fff;
  border-radius: 16rpx;
  width: 100%;
  max-width: 680rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.offline-payment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.offline-payment-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.offline-payment-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  font-size: 32rpx;
  font-weight: bold;
}

.offline-payment-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.offline-payment-greeting {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
  line-height: 1;
}

.offline-payment-description {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.payment-info-item {
  display: flex;
  margin-bottom: 24rpx;
  align-items: flex-start;
}

.payment-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

.offline-payment-notice {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-top: 32rpx;
  margin-bottom: 16rpx;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid var(--themeColor, #1989fa);
}

.offline-payment-thanks {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.5;
}

.offline-payment-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 24rpx;
}

.copy-btn {
  flex: 1;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid var(--themeColor, #1989fa);
  color: var(--themeColor, #1989fa);
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.copy-btn:active {
  background: var(--themeColor, #1989fa);
  color: #fff;
}

.confirm-btn {
  flex: 1;
  height: 88rpx;
  background: var(--themeColor, #1989fa);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}