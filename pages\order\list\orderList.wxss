/* pages/order/list/orderList.wxss */
page {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  overflow: hidden;
  height: calc(100vh - env(safe-area-inset-bottom));
}

.refresher-loader-order {
  height: calc(100vh - env(safe-area-inset-bottom) - 100rpx) !important;
  padding: 0 20rpx;
  box-sizing: border-box;
}

/* 订单卡片样式 */
.order-card {
  margin-bottom: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.order-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
}

.order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #edbf46 0%, #f4d03f 50%, #edbf46 100%);
}

/* 卡片头部样式 */
.order-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.order-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  border: 2rpx solid #ffeaa7;
}

.order-header-info {
  display: flex;
  flex-direction: column;
}

.order-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

/* 状态徽章样式 */
.order-status-badge {
  margin-left: 16rpx;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff7e6;
  color: #faad14;
  border: 1rpx solid #ffd666;
}

.status-shipping {
  background: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

.status-receiving {
  background: #fff1f0;
  color: #ff4d4f;
  border: 1rpx solid #ffadd2;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.status-cancelled {
  background: #f5f5f5;
  color: #8c8c8c;
  border: 1rpx solid #d9d9d9;
}

.status-refunded {
  background: #f0f5ff;
  color: #2f54eb;
  border: 1rpx solid #adc6ff;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-dot-pending {
  background: #faad14;
}

.status-dot-shipping {
  background: #1890ff;
  animation: pulse 2s infinite;
}

.status-dot-receiving {
  background: #ff4d4f;
}

.status-dot-completed {
  background: #52c41a;
}

.status-dot-cancelled {
  background: #8c8c8c;
}

.status-dot-refunded {
  background: #2f54eb;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 商品区域样式 */
.goods-section {
  padding: 20rpx 32rpx;
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image-wrapper {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
}

.goods-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 16rpx;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
}

.goods-spec {
  font-size: 20rpx;
  color: #999;
}

.goods-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.goods-price {
  font-size: 28rpx;
  color: #fa4126;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.goods-quantity {
  font-size: 24rpx;
  color: #999;
}

/* 订单信息区域 */
.order-info-section {
  padding: 16rpx 32rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-remark {
  display: flex;
  align-items: center;
}

.remark-text {
  font-size: 24rpx;
  color: #666;
}

/* 卡片底部操作区域 */
.order-card-footer {
  padding: 20rpx 32rpx 24rpx;
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
}

/* 总价与按钮平行布局 */
.footer-layout {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.total-price-wrapper {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.total-price-placeholder {
  width: 0;
  height: 0;
}

.total-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 12rpx;
}

.total-price {
  font-size: 32rpx;
  color: #fa4126;
  font-weight: 600;
}

/* 操作按钮区域 */
.card-actions {
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
}

.completed-actions,
.other-actions {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 110rpx;
  height: 46rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 主要按钮 */
.primary-btn {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: #fff;
  border: 1rpx solid #1890ff;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
}

.primary-btn:active {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(1rpx);
}

/* 次要按钮 */
.secondary-btn {
  background: #fff;
  color: #666;
  border: 1rpx solid #d9d9d9;
}

.secondary-btn:active {
  background: #f5f5f5;
  border-color: #bfbfbf;
}

/* 发票按钮 */
.invoice-btn {
  background: #edbf46;
  color: #ffffff;
  border: 1rpx solid #edbf46;
}

.invoice-btn:active {
  background: #d4ac0d;
  border-color: #d4ac0d;
}

/* 警告按钮 */
.warning-btn {
  background: #fff;
  color: #faad14;
  border: 1rpx solid #faad14;
}

.warning-btn:active {
  background: #fff7e6;
}

/* 危险按钮 */
.danger-btn {
  background: #fff;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}

.danger-btn:active {
  background: #fff1f0;
}

/* 信息按钮 */
.info-btn {
  background: #fff;
  color: #1890ff;
  border: 1rpx solid #1890ff;
}

.info-btn:active {
  background: #f0f8ff;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .order-card {
    margin-bottom: 20rpx;
    border-radius: 16rpx;
  }

  .order-card-header {
    padding: 20rpx 24rpx 16rpx;
  }

  .goods-section {
    padding: 16rpx 24rpx;
  }

  .order-card-footer {
    padding: 16rpx 24rpx 20rpx;
  }

  .action-btn {
    min-width: 90rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    height: 40rpx;
  }
}

.wr-refresher {
  height: calc(100vh - 96rpx) !important;
}

/* 申请退款按钮样式覆盖 */
.refund-btn {
  border-radius: 999rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f5f5;
  border: 2rpx solid #f5f5f5;
  text-align: center;
}

/* 撤销申请按钮样式 */
.cancel-refund-btn {
  border-radius: 999rpx;
  font-size: 26rpx;
  color: #ff6b6b;
  background-color: #fff5f5;
  border: 2rpx solid #ffebee;
  text-align: center;
}

/* 审核结果按钮样式 */
.audit-result-btn {
  border-radius: 999rpx;
  font-size: 26rpx;
  color: #2492F2;
  background-color: #f0f8ff;
  border: 2rpx solid #e6f3ff;
  text-align: center;
}

/* 线下支付信息弹窗样式 */
.offline-payment-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx;
  box-sizing: border-box;
}

.offline-payment-content {
  background: #fff;
  border-radius: 16rpx;
  width: 100%;
  max-width: 680rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.offline-payment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.offline-payment-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.offline-payment-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  color: #666;
  font-size: 32rpx;
  font-weight: bold;
}

.offline-payment-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.offline-payment-greeting {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
  line-height: 1;
}

.offline-payment-description {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.payment-info-item {
  display: flex;
  margin-bottom: 24rpx;
  align-items: flex-start;
}

.payment-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

.offline-payment-notice {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-top: 32rpx;
  margin-bottom: 16rpx;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid var(--themeColor, #1989fa);
}

.offline-payment-thanks {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.5;
}

.offline-payment-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 24rpx;
}

.copy-btn {
  flex: 1;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid var(--themeColor, #1989fa);
  color: var(--themeColor, #1989fa);
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.copy-btn:active {
  background: var(--themeColor, #1989fa);
  color: #fff;
}

.confirm-btn {
  flex: 1;
  height: 88rpx;
  background: var(--themeColor, #1989fa);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

/* 发票按钮样式 - 淡红色 */
.invoice-btn {
  background: #edbf46;
  color: #ffffff;
  border: 1rpx solid #edbf46;
  border-radius: 25rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.invoice-btn:active {
  background: #ffcdd2;
  border-color: #ef9a9a;
}