<!--pages/comment/add/add.wxml-->
<view class="comment-container">
  <!-- 商品信息 -->
  <view class="goods-info">
    <view class="goods-item">
      <image class="goods-image" src="{{goodsInfo.pic}}" mode="aspectFill" />
      <view class="goods-details">
        <view class="goods-name">{{goodsInfo.goodsName}}</view>
        <view class="goods-spec">规格: {{goodsInfo.specDesc || '默认'}}</view>
        <view class="goods-price">
          <price price="{{goodsInfo.price * 100}}" />
        </view>
      </view>
    </view>
  </view>

  <!-- 评分区域 -->
  <view class="rating-section">
    <view class="section-title">商品评分</view>
    <view class="rating-container">
      <van-rate
        value="{{rating}}"
        size="24"
        color="#2492F2"
        void-color="#E5E5E5"
        gutter="4"
        bind:change="onRatingChange"
        allow-half="{{false}}"
      />
      <view class="rating-text">
        <text wx:if="{{rating === 0}}">请选择评分</text>
        <text wx:elif="{{rating === 1}}">很差</text>
        <text wx:elif="{{rating === 2}}">较差</text>
        <text wx:elif="{{rating === 3}}">一般</text>
        <text wx:elif="{{rating === 4}}">满意</text>
        <text wx:elif="{{rating === 5}}">非常满意</text>
      </view>
    </view>
  </view>

  <!-- 评价内容 -->
  <view class="content-section">
    <view class="section-title">评价内容</view>
    <textarea 
      class="content-textarea"
      placeholder="请输入您的评价内容..."
      value="{{content}}"
      maxlength="500"
      bind:input="onContentInput"
      auto-height
      show-confirm-bar="{{false}}"
    />
    <view class="content-count">{{content.length}}/500</view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{submitLoading ? 'loading' : ''}}" 
      bind:tap="submitComment"
      disabled="{{submitLoading}}"
    >
      {{submitLoading ? '提交中...' : '提交评价'}}
    </button>
  </view>
</view>
