// pages/my/components/order-group/order-group.js
import { checkLogin } from "~/api/AuthApi.js"

Component({
  properties: {
    orderNums: {
      type: Object,
      value: { waitPay: 0, waitDelivery: 0, waitReceive: 0 },
    },
  },
  data: {},
  methods: {
    gotoOrderList(e) {
      checkLogin().then(res => {
        const { which } = e.currentTarget.dataset;
        wx.navigateTo({ url: `/pages/order/list/orderList?which=${which}` })
      }).catch((e) => {
        wx.navigateTo({ url: '/pages/auth/auth' });
      });
    }
  }
});
