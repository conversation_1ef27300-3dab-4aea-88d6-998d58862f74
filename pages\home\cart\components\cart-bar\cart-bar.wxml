<view class="cart-bar">
  <t-icon size="40rpx" color="{{isAllSelected ? 'var(--themeColor)' : '#BBBBBB'}}" name="{{isAllSelected ? 'check-circle-filled' : 'circle'}}" class="cart-bar__check" catch:tap="handleSelectAll" />
  <text style="color: #333;font-size: 30rpx;">全选</text>
  <view class="cart-bar__total">
    <view>
      <text class="cart-bar__total--bold" style="padding-right: 4rpx;">总计</text>
      <price price="{{totalAmount || '0'}}" fill="{{false}}" decimalSmaller class="cart-bar__total--bold cart-bar__total--price" />
      <!-- <text class="cart-bar__total--normal">（不含运费）</text> -->
    </view>
    <!-- <view wx:if="{{totalDiscountAmount}}">
      <text class="cart-bar__total--normal" style="padding-right: 4rpx;">已优惠</text>
      <price class="cart-bar__total--normal" price="{{totalDiscountAmount || '0'}}" fill="{{false}}" />
    </view> -->
  </view>
  <view catch:tap="handleToSettle" class="{{!isDisabled ? '' : 'disabled-btn'}} account-btn" hover-class="{{isDisabled ? '' : 'hover-btn'}}">去结算({{totalGoodsNum}}) </view>
</view>