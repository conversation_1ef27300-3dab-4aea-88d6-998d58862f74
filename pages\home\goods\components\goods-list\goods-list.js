// pages/goods/components/goods-item/goods-item.js
import { checkLogin } from "~/api/AuthApi.js"
import { shopCarAdd, shopCarSubtract } from "~/api/ShopCarApi.js"

Component({
  properties: {
    goodsList: { type: Array },
  },
  data: {
  },
  methods: {
    toAddGoods(e) {
      const { goods } = e.currentTarget.dataset;
      this.triggerEvent('toAddGoods', { goods: goods });
    },
    toGoodsDetail(e) {
      const { goods } = e.currentTarget.dataset;
      wx.navigateTo({ url: '/pages/goodsDetail/goodsDetail?goodsId=' + goods.id });
    },
  }
})