// pages/order/settle/settle.js
import { shopCarList } from "~/api/ShopCarApi.js"
import { orderCreate, getWxPay } from '~/api/OrderApi';
import * as GoodsApi from "~/api/GoodsApi.js"
import UserAddressApi from '~/api/UserAddressApi';
import { getAddressPromise } from '~/pages/home/<USER>/address/util';

Page({
  data: {
    address: {},
    goodsList: [],
    remark: ''
  },
  gotoSelectAddress() {
    getAddressPromise().then((address) => {
      this.setData({ address: address });
    }).catch(() => { });
    wx.navigateTo({ url: '/pages/home/<USER>/address/address?mode=select' })
  },
  doCreateOrder() {
    if (!this.data.address.id) {
      wx.showToast({ title: '请选择联系方式', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '正在提交...', mask: true });
    const params = {
      remark: this.data.remark,
      payMethod: "wxpay",
      deliveryMethod: 3,
      addressId: this.data.address.id
    };
    params.goodsList = this.data.goodsList.map((item) => {
      return {
        shopCarId: item.shopCarId || "",
        buyNum: item.buyNum,
        goodsId: item.id,
        merchantId: item.merchantId,
        goodsItemId: item.goodsItem.id
      };
    });
    orderCreate(params).then(res => {
      wx.hideLoading();
      if (res.orderCode) {
        wx.redirectTo({ url: `/pages/order/orderDetail/orderDetail?orderCode=${res.orderCode}` });
      } else {
        wx.redirectTo({ url: `/pages/order/list/orderList?which=1` })
      }
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
    });
  },
  async init(ids) {
    try {
      const result = await shopCarList({ ids });
      const list = result.map(item => {
        item.goods.shopCarId = item.id;
        item.goods.goodsItem = item.goodsItem;
        item.goods.buyNum = item.goodsNum;
        if (item.goods.specType === 1) {
          item.goods.specStr = "默认";
        } else if (item.goods.specType === 2) {
          item.goods.specStr = item.specDesc;
        }
        return item.goods;
      });
      this.setData({ goodsList: list });
    } catch (error) {
      console.error('err:', error);
    }
  },
  async initGoods(goodsId, goodsItemId, buyNum) {
    try {
      const result = await GoodsApi.goodsInfo({ id: goodsId });
      result.goodsItem = result.goodsItemList.find(item => item.id === goodsItemId);
      result.buyNum = Number(buyNum);
      if (result.specType === 1) {
        result.specStr = "默认";
      }
      this.setData({ goodsList: [result] });
    } catch (error) {
      console.error('err:', error);
    }
  },
  remarkInput(e) {
    this.setData({ remark: e.detail.value });
  },
  onLoad(options) {
    if (options.cartIds) {
      this.init(options.cartIds);
    } else if (options.goodsId) {
      this.initGoods(options.goodsId, options.goodsItemId, options.buyNum);
    }
  },
  onShow() {

  },
  onHide() {

  }
})