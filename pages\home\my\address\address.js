// pages/my/address.js
import { userAddressList, userAddressDelete } from '~/api/UserAddressApi';
import { resolveAddress, rejectAddress } from './util';

Page({
  data: {
    selectMode: false,
    addressList: []
  },
  async init() {
    try {
      const result = await userAddressList();
      this.setData({ addressList: result.records });
    } catch (error) {
      console.error('err:', error);
    }
  },
  gotoAdd() {
    wx.navigateTo({ url: '/pages/home/<USER>/address/edit/editAddress' })
  },
  gotoEdit(e) {
    const item = e.currentTarget.dataset.item;
    wx.navigateTo({ url: '/pages/home/<USER>/address/edit/editAddress?id=' + item.id })
  },
  onDelete(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认删除吗？',
      success(res) {
        if (res.confirm) {
          const item = e.currentTarget.dataset.item;
          wx.showLoading({ title: '正在提交...', mask: true });
          userAddressDelete(item.id).then(res => {
            _that.init(true);
            wx.hideLoading();
          }).catch(error => {
            wx.hideLoading();
          });
        } else if (res.cancel) {
        }
      }
    });
  },
  doSelect(e) {
    if (this.data.selectMode) {
      const { item } = e.currentTarget.dataset;
      resolveAddress(item);
      wx.navigateBack({ delta: 1 });
    }
  },
  onLoad(options) {
    this.setData({ selectMode: "select" == options.mode });
  },
  onShow() {
    this.init(true);
  }
})