.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-row-center {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 单行文字 */
.lines-1 {
  word-break: break-all;
  overflow: hidden;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

/* 两行文字 */
.lines-2 {
  word-break: break-all;
  overflow: hidden;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.outline-btn {
  border-radius: 999rpx;
  font-size: 30rpx;
  color: #333;
  border: 2rpx solid #333;
  text-align: center;
}

.theme-btn {
  border-radius: 999rpx;
  background-color: var(--themeColor);
  border: 2rpx solid var(--themeColor);
  font-size: 30rpx;
  color: #ffffff;
  text-align: center;
}

.red-btn {
  border-radius: 999rpx;
  background-color: #fa4126;
  border: 2rpx solid #fa4126;
  font-size: 30rpx;
  color: #ffffff;
  text-align: center;
}

.hover-btn {
  opacity: 0.7;
}