/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 32rpx;
  position: relative;
}

/* 左侧区域 */
.navbar-left {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
}

.back-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  margin-right: 10rpx;
}

.back-btn:active {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(0.9);
}

/* 楼宇选择器行样式 */
.building-selector-row {
  padding: 0 32rpx 12rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* 楼宇选择器样式 */
.building-selector {
  display: flex;
  align-items: flex-start;
  padding: 8rpx 16rpx;
  max-width: 300rpx;
  min-width: 120rpx;
}

.building-selector:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.98);
}

.location-icon {
  margin-top: 6rpx;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.building-text {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
  min-width: 0;
}

.building-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40rpx;
  line-height: 40rpx;
}

.building-name {
  font-size: 28rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.chevron-icon {
  margin-left: 8rpx;
  flex-shrink: 0;
}

.building-distance {
  font-size: 20rpx;
  font-weight: 400;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  margin-top: 2rpx;
}

/* 中间标题区域 */
.navbar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
}

.navbar-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
}

/* 右侧区域 */
.navbar-right {
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

/* 占位区域 */
.navbar-placeholder {
  width: 100%;
  background-color: transparent;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .navbar-content {
    padding: 0 24rpx;
  }

  .building-selector-row {
    padding: 0 24rpx;
  }

  .navbar-title {
    font-size: 28rpx;
  }

  .building-selector {
    max-width: 250rpx;
    padding: 6rpx 12rpx;
  }
}

