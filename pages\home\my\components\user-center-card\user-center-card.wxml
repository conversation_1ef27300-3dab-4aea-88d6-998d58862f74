<wxs module="utils">
  var hidePhoneNum = function (array) { if (!array) return; var mphone = array.substring(0, 3) + '****' + array.substring(7); return mphone; }
  module.exports = { hidePhoneNum: hidePhoneNum }
</wxs>
<view class="user-center-card">
  <!-- 未登录的情况 -->
  <block wx:if="{{currAuthStep === AuthStepType.ONE}}">
    <view class="user-center-card__header" bind:tap="gotoAuth">
      <t-avatar image="{{userInfo.avatarUrl || defaultAvatarUrl}}" class="user-center-card__header__avatar" />
      <view class="user-center-card__header__name">{{'请登录'}}</view>
    </view>
  </block>
  <!-- 已登录但未授权用户信息情况 -->
  <block wx:if="{{currAuthStep === AuthStepType.TWO}}">
    <view class="user-center-card__header">
      <t-avatar image="{{userInfo.avatar || defaultAvatarUrl}}" class="user-center-card__header__avatar" />
      <view class="user-center-card__header__name">{{userInfo.nickName || '微信用户'}}</view>
      <!-- 需要授权用户信息，通过slot添加弹窗 -->
      <view class="user-center-card__header__transparent" wx:if="{{isNeedGetUserInfo}}">
        <slot name="getUserInfo" />
      </view>
      <!-- 不需要授权用户信息，仍然触发gotoUserEditPage事件 -->
      <view class="user-center-card__header__transparent" wx:else></view>
    </view>
  </block>
  <!-- 已登录且已经授权用户信息的情况 -->
  <block wx:if="{{currAuthStep === AuthStepType.THREE}}">
    <view class="user-center-card__header" bind:tap="gotoUserEditPage">
      <t-avatar t-class="avatar" mode="aspectFill" class="user-center-card__header__avatar" image="{{userInfo.avatar || defaultAvatarUrl}}" />
      <view>
        <view class="user-center-card__header__name">{{userInfo.nickName || '微信用户'}}</view>
        <view class="user-center-card__header__mobile">{{utils.hidePhoneNum(userInfo.mobile|| '') }}</view>
      </view>
    </view>
  </block>
</view>