/* components/floating-kefu/floating-kefu.wxss */
.floating-kefu-area {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
}

.floating-kefu-btn {
  width: 100rpx;
  height: 100rpx;
  pointer-events: auto;
}

.kefu-icon {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF9800 0%, #FF5722 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.4);
  transition: all 0.3s ease;
}

.floating-kefu-btn:active .kefu-icon {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.6);
}

/* 呼吸动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.4);
  }
  50% {
    box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.6), 0 0 0 8rpx rgba(255, 152, 0, 0.1);
  }
  100% {
    box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.4);
  }
}

.kefu-icon {
  animation: pulse 2s infinite;
}
