<!--pages/home/<USER>/personInfo/personInfo.wxml-->
<wxs module="utils">
  var hidePhoneNum = function (array) { if (!array) return; var mphone = array.substring(0, 3) + '****' + array.substring(7); return mphone; }
  module.exports = { hidePhoneNum: hidePhoneNum }
</wxs>

<view class="person-info">
  <t-cell-group>
    <t-cell title="头像" center="{{true}}" data-type="avatar" bind:click="onClickCell" arrow t-class-left="order-group__left" bind:tap="toEditInfo">
      <t-avatar slot="note" image="{{userInfo.avatar || defaultAvatarUrl}}" size="small" class="user-center-card__header__avatar" />
    </t-cell>
    <t-cell title="昵称" arrow note="{{userInfo.nickName}}" data-type="name" bind:click="onClickCell" t-class="t-cell-class" t-class-left="order-group__left" bind:tap="toEditInfo" />
    <t-cell bordered="{{false}}" title="手机号" arrow note="{{userInfo.mobile ? utils.hidePhoneNum(userInfo.mobile|| '') : '去绑定手机号'}}" data-type="phoneNumber" bind:click="onClickCell" t-class="t-cell-class" t-class-left="order-group__left" />
  </t-cell-group>
</view>
<t-toast id="t-toast" />